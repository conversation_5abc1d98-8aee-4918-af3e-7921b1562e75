@echo off
echo ========================================
echo Desktop Clock Quick Build Script
echo ========================================
echo.

REM Check if source file exists
if not exist "MiniClock.cpp" (
    echo Error: MiniClock.cpp not found
    echo Please make sure the file exists in current directory
    pause
    exit /b 1
)

echo Compiling MiniClock.cpp...
echo This is the simplest version with highest success rate
echo.

REM Try to find Visual Studio environment
set "FOUND_VS=0"

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo Found Visual Studio 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "FOUND_VS=1"
    goto :compile
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo Found Visual Studio 2022 Professional
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "FOUND_VS=1"
    goto :compile
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo Found Visual Studio 2019 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "FOUND_VS=1"
    goto :compile
)

REM Check if already in VS environment
where cl >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo Detected configured build environment
    set "FOUND_VS=1"
    goto :compile
)

echo Visual Studio build environment not found
echo.
echo Please install one of the following:
echo 1. Visual Studio 2022 Community (Recommended)
echo    https://visualstudio.microsoft.com/vs/community/
echo.
echo 2. Visual Studio Build Tools
echo    https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
echo.
echo During installation, select "C++ build tools" or "Desktop development with C++"
echo.
pause
exit /b 1

:compile
echo Starting compilation...
echo.

REM Compile command - using simplest parameters
cl /nologo /EHsc /O2 /MT /DUNICODE /D_UNICODE MiniClock.cpp /link user32.lib gdi32.lib kernel32.lib /SUBSYSTEM:WINDOWS /OUT:DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESS!
    echo ========================================
    echo.

    if exist DesktopClock.exe (
        echo Generated file: DesktopClock.exe

        REM Show file info
        for %%I in (DesktopClock.exe) do (
            echo File size: %%~zI bytes
        )

        echo.
        echo Program features:
        echo   - Transparent topmost time display
        echo   - Left-click drag to move
        echo   - Right-click to exit
        echo   - Segoe UI Light font
        echo   - Text shadow effect
        echo.
        echo Usage:
        echo   Double-click DesktopClock.exe to run
        echo.

        REM Ask if run immediately
        set /p "run_now=Run program now? (y/n): "
        if /i "%run_now%"=="y" (
            echo Starting program...
            start DesktopClock.exe
        )
    ) else (
        echo Error: Generated executable not found
    )
) else (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo Possible solutions:
    echo 1. Make sure Visual Studio is properly installed
    echo 2. Check if source code file is complete
    echo 3. Try running this script as administrator
    echo.
    echo If problem persists, try compiling other versions:
    echo   - BasicClock.cpp (medium complexity)
    echo   - ClockApp.cpp (full features)
)

REM Clean temporary files
if exist *.obj del *.obj >nul 2>&1
if exist *.pdb del *.pdb >nul 2>&1

echo.
pause
