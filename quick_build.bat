@echo off
echo ========================================
echo 桌面时钟快速编译脚本
echo ========================================
echo.

REM 检查是否存在推荐的源文件
if not exist "MiniClock.cpp" (
    echo 错误: 找不到 MiniClock.cpp 文件
    echo 请确保该文件存在于当前目录
    pause
    exit /b 1
)

echo 正在编译 MiniClock.cpp...
echo 这是最简单的版本，编译成功率最高
echo.

REM 尝试找到Visual Studio环境
set "FOUND_VS=0"

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到 Visual Studio 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "FOUND_VS=1"
    goto :compile
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到 Visual Studio 2022 Professional
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "FOUND_VS=1"
    goto :compile
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到 Visual Studio 2019 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set "FOUND_VS=1"
    goto :compile
)

REM 检查是否已经在VS环境中
where cl >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo 检测到已配置的编译环境
    set "FOUND_VS=1"
    goto :compile
)

echo 未找到 Visual Studio 编译环境
echo.
echo 请安装以下任一工具:
echo 1. Visual Studio 2022 Community (推荐)
echo    https://visualstudio.microsoft.com/vs/community/
echo.
echo 2. Visual Studio Build Tools
echo    https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
echo.
echo 安装时请选择 "C++ 构建工具" 或 "使用C++的桌面开发"
echo.
pause
exit /b 1

:compile
echo 开始编译...
echo.

REM 编译命令 - 使用最简单的参数
cl /nologo /EHsc /O2 /MT /DUNICODE /D_UNICODE MiniClock.cpp /link user32.lib gdi32.lib kernel32.lib /SUBSYSTEM:WINDOWS /OUT:DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo.
    echo ========================================
    echo 🎉 编译成功！
    echo ========================================
    echo.
    
    if exist DesktopClock.exe (
        echo ✅ 生成文件: DesktopClock.exe
        
        REM 显示文件信息
        for %%I in (DesktopClock.exe) do (
            echo 📦 文件大小: %%~zI 字节
        )
        
        echo.
        echo 🚀 程序功能:
        echo   • 透明置顶显示时间
        echo   • 左键拖拽移动位置
        echo   • 右键点击退出程序
        echo   • 使用 Segoe UI Light 字体
        echo   • 带有文字阴影效果
        echo.
        echo 💡 使用方法:
        echo   双击 DesktopClock.exe 运行程序
        echo.
        
        REM 询问是否立即运行
        set /p "run_now=是否立即运行程序? (y/n): "
        if /i "%run_now%"=="y" (
            echo 正在启动程序...
            start DesktopClock.exe
        )
    ) else (
        echo ❌ 错误: 未找到生成的可执行文件
    )
) else (
    echo.
    echo ========================================
    echo ❌ 编译失败
    echo ========================================
    echo.
    echo 可能的解决方案:
    echo 1. 确保已正确安装 Visual Studio
    echo 2. 检查源代码文件是否完整
    echo 3. 尝试以管理员身份运行此脚本
    echo.
    echo 如果问题持续，请尝试编译其他版本:
    echo   • BasicClock.cpp (中等复杂度)
    echo   • ClockApp.cpp (功能最完整)
)

REM 清理临时文件
if exist *.obj del *.obj >nul 2>&1
if exist *.pdb del *.pdb >nul 2>&1

echo.
pause
