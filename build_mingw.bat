@echo off
echo Building Desktop Clock with MinGW-w64...

REM Check if MinGW-w64 is available
where g++ >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo MinGW-w64 not found. Please install MinGW-w64 or MSYS2.
    echo You can download it from: https://www.msys2.org/
    pause
    exit /b 1
)

REM Check if windres is available
where windres >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo windres not found. Please make sure MinGW-w64 is properly installed.
    pause
    exit /b 1
)

REM Compile resource file
echo Compiling resources...
windres DesktopClock.rc -O coff -o DesktopClock.res
if %ERRORLEVEL% neq 0 (
    echo Resource compilation failed
    pause
    exit /b 1
)

REM Compile program
echo Compiling program...
g++ -std=c++17 -O2 -s -static -mwindows -DUNICODE -D_UNICODE ^
    main.cpp DesktopClock.cpp DesktopClock.res ^
    -lgdiplus -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 ^
    -o DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo Build successful! Generated: DesktopClock.exe
    dir DesktopClock.exe
    echo.
    echo File size:
    for %%I in (DesktopClock.exe) do echo %%~zI bytes
) else (
    echo Build failed!
)

REM Clean temporary files
del *.res 2>nul
del *.o 2>nul

pause
