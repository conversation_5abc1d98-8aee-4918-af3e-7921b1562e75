# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: DesktopClock
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# localized /showIncludes string

msvc_deps_prefix = 注意: 包含文件:  


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__DesktopClock_unscanned_Debug
  deps = msvc
  command = ${LAUNCHER}${CODE_CHECK}D:\vs\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe  /nologo /TP $DEFINES $INCLUDES $FLAGS /showIncludes /Fo$out /Fd$TARGET_COMPILE_PDB /FS -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling RC files.

rule RC_COMPILER__DesktopClock_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmcldeps.exe RC $in $DEP_FILE $out "注意: 包含文件:  " "D:/vs/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe" ${LAUNCHER}${CODE_CHECK}"D:\Windows Kits\10\bin\10.0.26100.0\x64\rc.exe" $DEFINES $INCLUDES $FLAGS /fo $out $in
  description = Building RC object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__DesktopClock_Debug
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && D:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -E vs_link_exe --msvc-ver=1944 --intdir=$OBJECT_DIR --rc="D:\Windows Kits\10\bin\10.0.26100.0\x64\rc.exe" --mt="D:\Windows Kits\10\bin\10.0.26100.0\x64\mt.exe" --manifests $MANIFESTS -- D:\vs\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe /nologo $in  /out:$TARGET_FILE /implib:$TARGET_IMPLIB /pdb:$TARGET_PDB /version:0.0 $LINK_FLAGS $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\time -BC:\Users\<USER>\Desktop\time\out\build\x64-Debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = d:\vs\common7\ide\commonextensions\microsoft\cmake\Ninja\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = d:\vs\common7\ide\commonextensions\microsoft\cmake\Ninja\ninja.exe -t targets
  description = All primary targets available:

