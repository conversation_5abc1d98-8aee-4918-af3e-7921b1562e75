@echo off
echo Building Simple VB.NET Clock...

REM Check if .NET is available
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo .NET SDK not found
    echo Please install .NET 6.0 SDK from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET SDK found:
dotnet --version

echo.
echo Cleaning previous build...
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj

echo Building SimpleClock project...
dotnet build SimpleClock.vbproj -c Release --verbosity minimal

if %ERRORLEVEL% == 0 (
    echo.
    echo BUILD SUCCESS!
    
    REM Find and copy the executable
    for /r bin\Release %%i in (SimpleClock.exe) do (
        if exist "%%i" (
            echo Generated: %%i
            copy "%%i" "SimpleClock.exe" >nul
            if exist SimpleClock.exe (
                echo Copied to: SimpleClock.exe
                echo.
                echo Features:
                echo - Transparent topmost display
                echo - Left-click drag to move  
                echo - Right-click for menu
                echo - Shows current time HH:MM:SS
                echo.
                set /p "run_now=Run program now? (y/n): "
                if /i "!run_now!"=="y" (
                    echo Starting SimpleClock...
                    start SimpleClock.exe
                )
            )
            goto :end
        )
    )
    echo Warning: Executable not found
) else (
    echo BUILD FAILED
    echo Check error messages above
)

:end
pause
