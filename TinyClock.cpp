#include <windows.h>
#include <string>

HWND g_hWnd = nullptr;
HFONT g_hFont = nullptr;

std::wstring GetTime() {
    SYSTEMTIME st;
    GetLocalTime(&st);
    wchar_t buf[32];
    wsprintf(buf, L"%02d:%02d:%02d", st.wHour, st.wMinute, st.wSecond);
    return std::wstring(buf);
}

void OnPaint(HWND hWnd) {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hWnd, &ps);
    
    SetBkMode(hdc, TRANSPARENT);
    SelectObject(hdc, g_hFont);
    
    std::wstring time = GetTime();
    RECT rect;
    GetClientRect(hWnd, &rect);
    
    SetTextColor(hdc, RGB(255, 255, 255));
    DrawText(hdc, time.c_str(), -1, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
    
    EndPaint(hWnd, &ps);
}

LRESULT CALLBACK WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    static bool dragging = false;
    static POINT dragStart;
    
    switch (msg) {
    case WM_CREATE:
        g_hFont = CreateFont(48, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                           DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                           DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, L"Arial");
        SetTimer(hWnd, 1, 1000, nullptr);
        return 0;
        
    case WM_PAINT:
        OnPaint(hWnd);
        return 0;
        
    case WM_TIMER:
        InvalidateRect(hWnd, nullptr, FALSE);
        return 0;
        
    case WM_LBUTTONDOWN:
        dragging = true;
        SetCapture(hWnd);
        dragStart.x = LOWORD(lParam);
        dragStart.y = HIWORD(lParam);
        return 0;
        
    case WM_LBUTTONUP:
        if (dragging) {
            dragging = false;
            ReleaseCapture();
        }
        return 0;
        
    case WM_MOUSEMOVE:
        if (dragging) {
            POINT pt;
            GetCursorPos(&pt);
            SetWindowPos(hWnd, nullptr, pt.x - dragStart.x, pt.y - dragStart.y, 
                        0, 0, SWP_NOSIZE | SWP_NOZORDER);
        }
        return 0;
        
    case WM_RBUTTONUP:
        if (MessageBox(hWnd, L"Exit clock?", L"Confirm", MB_YESNO) == IDYES) {
            PostQuitMessage(0);
        }
        return 0;
        
    case WM_DESTROY:
        KillTimer(hWnd, 1);
        if (g_hFont) DeleteObject(g_hFont);
        PostQuitMessage(0);
        return 0;
    }
    
    return DefWindowProc(hWnd, msg, wParam, lParam);
}

int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE, PWSTR, int) {
    const wchar_t* className = L"TinyClock";
    
    WNDCLASS wc = {};
    wc.lpfnWndProc = WndProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = className;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH);
    
    RegisterClass(&wc);
    
    g_hWnd = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST,
        className, L"Tiny Clock", WS_POPUP,
        100, 100, 250, 80,
        nullptr, nullptr, hInstance, nullptr
    );
    
    SetLayeredWindowAttributes(g_hWnd, RGB(0, 0, 0), 200, LWA_COLORKEY | LWA_ALPHA);
    
    ShowWindow(g_hWnd, SW_SHOW);
    UpdateWindow(g_hWnd);
    
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
