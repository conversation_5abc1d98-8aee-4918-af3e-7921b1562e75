@echo off
echo ========================================
echo Building VB.NET Desktop Clock
echo ========================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: .NET SDK not found
    echo Please install .NET 6.0 SDK or later
    echo Download: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET SDK found
dotnet --version

echo.
echo Building project...

REM Clean previous build
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj

REM Build the project
dotnet build -c Release --verbosity minimal

if %ERRORLEVEL% == 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESS!
    echo ========================================
    echo.
    
    REM Find the executable
    for /r bin\Release %%i in (DesktopClock.exe) do (
        if exist "%%i" (
            echo Generated: %%i
            
            REM Get file size
            for %%j in ("%%i") do (
                echo Size: %%~zj bytes
            )
            
            echo.
            echo Features:
            echo - Transparent topmost display
            echo - Left-click drag to move
            echo - Right-click for settings menu
            echo - Auto-save configuration
            echo - Multiple display options
            echo.
            
            REM Copy to root directory for easy access
            copy "%%i" "DesktopClock.exe" >nul
            if exist DesktopClock.exe (
                echo Copied to: DesktopClock.exe
                echo.
                set /p "run_now=Run program now? (y/n): "
                if /i "!run_now!"=="y" (
                    echo Starting program...
                    start DesktopClock.exe
                )
            )
            goto :end
        )
    )
    
    echo Warning: Executable not found in expected location
) else (
    echo.
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo Check the error messages above
    echo.
    echo Common solutions:
    echo 1. Make sure .NET 6.0 SDK is installed
    echo 2. Check if all source files are present
    echo 3. Try running: dotnet restore
)

:end
echo.
pause
