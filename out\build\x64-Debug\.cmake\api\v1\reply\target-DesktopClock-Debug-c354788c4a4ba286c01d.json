{"artifacts": [{"path": "DesktopClock.exe"}, {"path": "DesktopClock.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_definitions"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 39, "parent": 0}, {"command": 1, "file": 0, "line": 64, "parent": 0}, {"command": 2, "file": 0, "line": 42, "parent": 0}, {"command": 3, "file": 0, "line": 18, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}], "defines": [{"backtrace": 4, "define": "UNICODE"}, {"backtrace": 4, "define": "_UNICODE"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 4, "define": "UNICODE"}, {"backtrace": 4, "define": "_UNICODE"}], "language": "RC", "sourceIndexes": [4]}], "id": "DesktopClock::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Users/<USER>/Desktop/time/out/install/x64-Debug"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/SUBSYSTEM:WINDOWS /debug /INCREMENTAL /subsystem:windows /MANIFEST:EMBED /MANIFESTINPUT:C:/Users/<USER>/Desktop/time/app.manifest", "role": "flags"}, {"backtrace": 3, "fragment": "gdiplus.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "comctl32.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "advapi32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "DesktopClock", "nameOnDisk": "DesktopClock.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 4]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 3]}, {"name": "", "sourceIndexes": [5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "DesktopClock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "DesktopClock.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "resource.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "DesktopClock.rc", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "app.manifest", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}