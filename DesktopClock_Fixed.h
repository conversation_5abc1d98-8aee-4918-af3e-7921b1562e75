#pragma once

#define UNICODE
#define _UNICODE

#include <windows.h>
#include <windowsx.h>
#include <gdiplus.h>
#include <commdlg.h>
#include <commctrl.h>
#include <string>
#include <memory>

#pragma comment(lib, "gdiplus.lib")
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")
#pragma comment(lib, "advapi32.lib")

using namespace Gdiplus;

// 资源ID定义
#define IDM_SETTINGS        1001
#define IDM_EXIT           1002
#define IDM_FONT           1003
#define IDM_COLOR          1004
#define IDM_TRANSPARENCY   1005
#define IDM_TOPMOST        1006
#define IDD_SETTINGS       2001
#define IDC_FONT_SIZE      3001
#define IDC_TRANSPARENCY   3002
#define IDC_TOPMOST        3003
#define IDC_COLOR_BUTTON   3004
#define IDC_FONT_BUTTON    3005
#define IDT_TIMER          4001

// 配置结构体
struct ClockConfig {
    int fontSize;
    Color textColor;
    int transparency;
    bool topmost;
    RECT position;
    std::wstring fontFamily;
    bool showSeconds;
    bool is24Hour;
    
    // 构造函数
    ClockConfig() : 
        fontSize(48),
        textColor(Color(255, 255, 255, 255)),
        transparency(240),
        topmost(true),
        showSeconds(true),
        is24Hour(true),
        fontFamily(L"Segoe UI Light")
    {
        position.left = 100;
        position.top = 100;
        position.right = 400;
        position.bottom = 200;
    }
};

class DesktopClock {
private:
    HWND m_hWnd;
    HINSTANCE m_hInstance;
    ClockConfig m_config;
    
    // GDI+ 相关
    ULONG_PTR m_gdiplusToken;
    std::unique_ptr<Graphics> m_graphics;
    std::unique_ptr<Font> m_font;
    std::unique_ptr<SolidBrush> m_textBrush;
    std::unique_ptr<SolidBrush> m_shadowBrush;
    
    // 窗口状态
    bool m_isDragging;
    POINT m_dragOffset;
    
    // 注册表键名
    static constexpr const wchar_t* REGISTRY_KEY = L"SOFTWARE\\DesktopClock";

public:
    DesktopClock(HINSTANCE hInstance);
    ~DesktopClock();
    
    // 初始化和清理
    bool Initialize();
    void Cleanup();
    
    // 窗口相关
    bool CreateClockWindow();
    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(UINT message, WPARAM wParam, LPARAM lParam);
    
    // 绘制相关
    void OnPaint();
    void UpdateTime();
    void InvalidateWindow();
    
    // 事件处理
    void OnTimer();
    void OnRightClick(int x, int y);
    void OnLeftButtonDown(int x, int y);
    void OnLeftButtonUp();
    void OnMouseMove(int x, int y);
    
    // 设置相关
    void ShowSettingsDialog();
    static INT_PTR CALLBACK SettingsDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);
    void ApplySettings();
    
    // 配置管理
    void LoadConfig();
    void SaveConfig();
    
    // 工具函数
    void UpdateWindowStyle();
    void UpdateFont();
    std::wstring GetCurrentTimeString();
    RECT GetTextBounds(const std::wstring& text);
    
    // 获取窗口句柄
    HWND GetHWnd() const { return m_hWnd; }
};
