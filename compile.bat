@echo off
echo 正在编译桌面时钟程序...

REM 尝试使用不同的Visual Studio版本
set "VSPATH="

REM 检查 Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :found
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto :found
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    goto :found
)

REM 检查 Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :found
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto :found
)

REM 检查 Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    goto :found
)

echo 错误: 未找到 Visual Studio 编译环境
echo 请安装 Visual Studio 2019 或 2022，或者 Visual Studio Build Tools
echo 下载地址: https://visualstudio.microsoft.com/downloads/
pause
exit /b 1

:found
echo 找到编译环境: %VSPATH%
call "%VSPATH%"

if %ERRORLEVEL% neq 0 (
    echo 错误: 无法初始化编译环境
    pause
    exit /b 1
)

echo 开始编译...

REM 编译程序
cl /EHsc /O2 /MT /DNDEBUG /DUNICODE /D_UNICODE ^
   ClockApp.cpp ^
   /link user32.lib gdi32.lib kernel32.lib advapi32.lib comdlg32.lib ^
   /SUBSYSTEM:WINDOWS ^
   /OUT:DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo 生成文件: DesktopClock.exe
    echo ========================================
    echo.
    
    if exist DesktopClock.exe (
        echo 文件信息:
        dir DesktopClock.exe | find "DesktopClock.exe"
        echo.
        echo 使用说明:
        echo - 双击运行 DesktopClock.exe
        echo - 左键拖拽移动时钟
        echo - 右键打开设置菜单
        echo - 程序会自动保存位置和设置
        echo.
    )
) else (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo 请检查错误信息并修复代码
)

REM 清理临时文件
if exist *.obj del *.obj
if exist *.pdb del *.pdb

echo.
pause
