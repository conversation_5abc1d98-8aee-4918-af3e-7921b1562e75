# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: DesktopClock
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\snqig\Desktop\time\out\build\x64-Debug\
# =============================================================================
# Object build statements for EXECUTABLE target DesktopClock


#############################################
# Order-only phony target for DesktopClock

build cmake_object_order_depends_target_DesktopClock: phony || .

build CMakeFiles\DesktopClock.dir\main.cpp.obj: CXX_COMPILER__DesktopClock_unscanned_Debug C$:\Users\snqig\Desktop\time\main.cpp || cmake_object_order_depends_target_DesktopClock
  DEFINES = -DUNICODE -D_UNICODE
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd
  OBJECT_DIR = CMakeFiles\DesktopClock.dir
  OBJECT_FILE_DIR = CMakeFiles\DesktopClock.dir
  TARGET_COMPILE_PDB = CMakeFiles\DesktopClock.dir\
  TARGET_PDB = DesktopClock.pdb

build CMakeFiles\DesktopClock.dir\DesktopClock.cpp.obj: CXX_COMPILER__DesktopClock_unscanned_Debug C$:\Users\snqig\Desktop\time\DesktopClock.cpp || cmake_object_order_depends_target_DesktopClock
  DEFINES = -DUNICODE -D_UNICODE
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd
  OBJECT_DIR = CMakeFiles\DesktopClock.dir
  OBJECT_FILE_DIR = CMakeFiles\DesktopClock.dir
  TARGET_COMPILE_PDB = CMakeFiles\DesktopClock.dir\
  TARGET_PDB = DesktopClock.pdb

build CMakeFiles\DesktopClock.dir\DesktopClock.rc.res: RC_COMPILER__DesktopClock_unscanned_Debug C$:\Users\snqig\Desktop\time\DesktopClock.rc || cmake_object_order_depends_target_DesktopClock
  DEFINES = -DUNICODE -D_UNICODE
  DEP_FILE = CMakeFiles\DesktopClock.dir\DesktopClock.rc.res.d
  FLAGS = -DWIN32 -D_DEBUG
  OBJECT_DIR = CMakeFiles\DesktopClock.dir
  OBJECT_FILE_DIR = CMakeFiles\DesktopClock.dir
  TARGET_COMPILE_PDB = CMakeFiles\DesktopClock.dir\
  TARGET_PDB = DesktopClock.pdb


# =============================================================================
# Link build statements for EXECUTABLE target DesktopClock


#############################################
# Link the executable DesktopClock.exe

build DesktopClock.exe: CXX_EXECUTABLE_LINKER__DesktopClock_Debug CMakeFiles\DesktopClock.dir\main.cpp.obj CMakeFiles\DesktopClock.dir\DesktopClock.cpp.obj CMakeFiles\DesktopClock.dir\DesktopClock.rc.res | C$:\Users\snqig\Desktop\time\app.manifest
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /SUBSYSTEM:WINDOWS /debug /INCREMENTAL /subsystem:windows /MANIFEST:EMBED /MANIFESTINPUT:C:/Users/<USER>/Desktop/time/app.manifest
  LINK_LIBRARIES = gdiplus.lib  comctl32.lib  user32.lib  gdi32.lib  kernel32.lib  advapi32.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  MANIFESTS = C:\Users\<USER>\Desktop\time\app.manifest
  OBJECT_DIR = CMakeFiles\DesktopClock.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\DesktopClock.dir\
  TARGET_FILE = DesktopClock.exe
  TARGET_IMPLIB = DesktopClock.lib
  TARGET_PDB = DesktopClock.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\time\out\build\x64-Debug && D:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\time\out\build\x64-Debug && D:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\time -BC:\Users\<USER>\Desktop\time\out\build\x64-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles\install.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\time\out\build\x64-Debug && D:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles\install.util


#############################################
# Utility command for install/local

build CMakeFiles\install\local.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\time\out\build\x64-Debug && D:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install\local: phony CMakeFiles\install\local.util

# =============================================================================
# Target aliases.

build DesktopClock: phony DesktopClock.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/time/out/build/x64-Debug

build all: phony DesktopClock.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Users\snqig\Desktop\time\CMakeLists.txt CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Users\snqig\Desktop\time\CMakeLists.txt CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake D$:\vs\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
