#pragma once

#include <windows.h>
#include <gdiplus.h>
#include <commdlg.h>
#include <commctrl.h>
#include <string>
#include <memory>
#include "resource.h"

#pragma comment(lib, "gdiplus.lib")
#pragma comment(lib, "comctl32.lib")

using namespace Gdiplus;

// 配置结构体
struct ClockConfig {
    int fontSize = 48;
    Color textColor = Color(255, 255, 255, 255);  // 白色
    int transparency = 240;  // 透明度 (0-255)
    bool topmost = true;
    RECT position = {100, 100, 300, 150};
    std::wstring fontFamily = L"Segoe UI Light";
    bool showSeconds = true;
    bool is24Hour = true;
};

class DesktopClock {
private:
    HWND m_hWnd;
    HINSTANCE m_hInstance;
    ClockConfig m_config;
    
    // GDI+ 相关
    ULONG_PTR m_gdiplusToken;
    std::unique_ptr<Graphics> m_graphics;
    std::unique_ptr<Font> m_font;
    std::unique_ptr<SolidBrush> m_textBrush;
    std::unique_ptr<SolidBrush> m_shadowBrush;
    
    // 窗口状态
    bool m_isDragging = false;
    POINT m_dragOffset = {0, 0};
    
    // 注册表键名
    static constexpr wchar_t* REGISTRY_KEY = L"SOFTWARE\\DesktopClock";

public:
    DesktopClock(HINSTANCE hInstance);
    ~DesktopClock();
    
    // 初始化和清理
    bool Initialize();
    void Cleanup();
    
    // 窗口相关
    bool CreateClockWindow();
    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(UINT message, WPARAM wParam, LPARAM lParam);
    
    // 绘制相关
    void OnPaint();
    void UpdateTime();
    void InvalidateWindow();
    
    // 事件处理
    void OnTimer();
    void OnRightClick(int x, int y);
    void OnLeftButtonDown(int x, int y);
    void OnLeftButtonUp();
    void OnMouseMove(int x, int y);
    
    // 设置相关
    void ShowSettingsDialog();
    static INT_PTR CALLBACK SettingsDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);
    void ApplySettings();
    
    // 配置管理
    void LoadConfig();
    void SaveConfig();
    
    // 工具函数
    void UpdateWindowStyle();
    void UpdateFont();
    std::wstring GetCurrentTimeString();
    RECT GetTextBounds(const std::wstring& text);
    
    // 获取窗口句柄
    HWND GetHWnd() const { return m_hWnd; }
};
