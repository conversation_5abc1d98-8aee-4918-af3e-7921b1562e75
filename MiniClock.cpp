#define UNICODE
#define _UNICODE
#include <windows.h>
#include <string>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")

HWND g_hWnd = nullptr;
HFONT g_hFont = nullptr;
bool g_dragging = false;
POINT g_dragStart = {0, 0};

std::wstring GetTimeString() {
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    wchar_t buffer[32];
    wsprintf(buffer, L"%02d:%02d:%02d", st.wHour, st.wMinute, st.wSecond);
    return std::wstring(buffer);
}

void OnPaint(HWND hWnd) {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hWnd, &ps);
    
    SetBkMode(hdc, TRANSPARENT);
    HFONT oldFont = (HFONT)SelectObject(hdc, g_hFont);
    
    std::wstring timeStr = GetTimeString();
    
    RECT rect;
    GetClientRect(hWnd, &rect);
    
    SIZE textSize;
    GetTextExtentPoint32(hdc, timeStr.c_str(), timeStr.length(), &textSize);
    
    int x = (rect.right - textSize.cx) / 2;
    int y = (rect.bottom - textSize.cy) / 2;
    
    // 阴影
    SetTextColor(hdc, RGB(64, 64, 64));
    TextOut(hdc, x + 2, y + 2, timeStr.c_str(), timeStr.length());
    
    // 主文字
    SetTextColor(hdc, RGB(255, 255, 255));
    TextOut(hdc, x, y, timeStr.c_str(), timeStr.length());
    
    SelectObject(hdc, oldFont);
    EndPaint(hWnd, &ps);
}

LRESULT CALLBACK WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch (msg) {
    case WM_CREATE:
        g_hFont = CreateFont(48, 0, 0, 0, FW_LIGHT, FALSE, FALSE, FALSE,
                           DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                           CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_SWISS, L"Segoe UI Light");
        SetTimer(hWnd, 1, 1000, nullptr);
        return 0;
        
    case WM_PAINT:
        OnPaint(hWnd);
        return 0;
        
    case WM_TIMER:
        InvalidateRect(hWnd, nullptr, FALSE);
        return 0;
        
    case WM_LBUTTONDOWN:
        g_dragging = true;
        SetCapture(hWnd);
        g_dragStart.x = LOWORD(lParam);
        g_dragStart.y = HIWORD(lParam);
        return 0;
        
    case WM_LBUTTONUP:
        if (g_dragging) {
            g_dragging = false;
            ReleaseCapture();
        }
        return 0;
        
    case WM_MOUSEMOVE:
        if (g_dragging) {
            POINT pt;
            GetCursorPos(&pt);
            SetWindowPos(hWnd, nullptr, pt.x - g_dragStart.x, pt.y - g_dragStart.y, 
                        0, 0, SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);
        }
        return 0;
        
    case WM_RBUTTONUP:
        if (IDYES == MessageBox(hWnd, L"退出桌面时钟？", L"确认", MB_YESNO | MB_ICONQUESTION)) {
            PostQuitMessage(0);
        }
        return 0;
        
    case WM_DESTROY:
        KillTimer(hWnd, 1);
        if (g_hFont) DeleteObject(g_hFont);
        PostQuitMessage(0);
        return 0;
    }
    
    return DefWindowProc(hWnd, msg, wParam, lParam);
}

int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE, PWSTR, int) {
    const wchar_t* className = L"MiniDesktopClock";
    
    WNDCLASS wc = {};
    wc.lpfnWndProc = WndProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = className;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH);
    
    if (!RegisterClass(&wc)) return -1;
    
    g_hWnd = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
        className, L"Mini Clock", WS_POPUP,
        100, 100, 300, 100,
        nullptr, nullptr, hInstance, nullptr
    );
    
    if (!g_hWnd) return -1;
    
    SetLayeredWindowAttributes(g_hWnd, RGB(0, 0, 0), 200, LWA_COLORKEY | LWA_ALPHA);
    
    ShowWindow(g_hWnd, SW_SHOW);
    UpdateWindow(g_hWnd);
    
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
