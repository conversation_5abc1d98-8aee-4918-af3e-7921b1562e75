Imports System
Imports System.Drawing
Imports System.Drawing.Drawing2D
Imports System.Windows.Forms
Imports Microsoft.Win32

Public Class ClockForm
    Inherits Form

    ' 私有字段
    Private WithEvents clockTimer As Timer
    Private isDragging As Boolean = False
    Private dragOffset As Point
    Private clockFont As Font
    Private textBrush As SolidBrush
    Private shadowBrush As SolidBrush
    
    ' 配置属性
    Private _fontSize As Integer = 48
    Private _textColor As Color = Color.White
    Private _transparency As Integer = 200
    Private _topMost As Boolean = True
    Private _showSeconds As Boolean = True
    Private _is24Hour As Boolean = True
    
    ' 注册表键名
    Private Const REGISTRY_KEY As String = "SOFTWARE\DesktopClockVB"

    Public Sub New()
        InitializeComponent()
        LoadSettings()
        SetupWindow()
        SetupTimer()
        UpdateFont()
    End Sub

    Private Sub InitializeComponent()
        ' 设置窗体基本属性
        Me.Text = "桌面时钟"
        Me.Size = New Size(300, 100)
        Me.StartPosition = FormStartPosition.Manual
        Me.Location = New Point(100, 100)
        Me.FormBorderStyle = FormBorderStyle.None
        Me.BackColor = Color.Black
        Me.ShowInTaskbar = False
        Me.TopMost = True
        
        ' 设置双缓冲以减少闪烁
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or 
                   ControlStyles.UserPaint Or 
                   ControlStyles.DoubleBuffer Or 
                   ControlStyles.ResizeRedraw, True)
        
        ' 设置透明键
        Me.TransparencyKey = Color.Black
    End Sub

    Private Sub SetupWindow()
        ' 设置窗体透明度
        Me.Opacity = _transparency / 255.0
        Me.TopMost = _topMost
        
        ' 创建画刷
        textBrush = New SolidBrush(_textColor)
        shadowBrush = New SolidBrush(Color.FromArgb(128, Color.Black))
    End Sub

    Private Sub SetupTimer()
        ' 创建并启动定时器
        clockTimer = New Timer()
        clockTimer.Interval = 1000 ' 1秒
        clockTimer.Enabled = True
    End Sub

    Private Sub UpdateFont()
        ' 释放旧字体
        If clockFont IsNot Nothing Then
            clockFont.Dispose()
        End If
        
        ' 创建新字体
        Try
            clockFont = New Font("Segoe UI Light", _fontSize, FontStyle.Regular)
        Catch
            ' 如果Segoe UI Light不可用，使用默认字体
            clockFont = New Font("Arial", _fontSize, FontStyle.Regular)
        End Try
    End Sub

    Private Function GetTimeString() As String
        Dim now As DateTime = DateTime.Now
        
        If _is24Hour Then
            If _showSeconds Then
                Return now.ToString("HH:mm:ss")
            Else
                Return now.ToString("HH:mm")
            End If
        Else
            If _showSeconds Then
                Return now.ToString("hh:mm:ss tt")
            Else
                Return now.ToString("hh:mm tt")
            End If
        End If
    End Function

    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        MyBase.OnPaint(e)
        
        Dim g As Graphics = e.Graphics
        g.SmoothingMode = SmoothingMode.AntiAlias
        g.TextRenderingHint = Drawing.Text.TextRenderingHint.AntiAlias
        
        Dim timeText As String = GetTimeString()
        Dim textSize As SizeF = g.MeasureString(timeText, clockFont)
        
        ' 计算居中位置
        Dim x As Single = (Me.Width - textSize.Width) / 2
        Dim y As Single = (Me.Height - textSize.Height) / 2
        
        ' 绘制阴影
        g.DrawString(timeText, clockFont, shadowBrush, x + 2, y + 2)
        
        ' 绘制主文字
        g.DrawString(timeText, clockFont, textBrush, x, y)
        
        ' 自动调整窗体大小
        Dim newWidth As Integer = CInt(textSize.Width) + 20
        Dim newHeight As Integer = CInt(textSize.Height) + 20
        
        If Me.Width <> newWidth OrElse Me.Height <> newHeight Then
            Me.Size = New Size(newWidth, newHeight)
        End If
    End Sub

    Private Sub ClockTimer_Tick(sender As Object, e As EventArgs) Handles clockTimer.Tick
        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnMouseDown(e As MouseEventArgs)
        MyBase.OnMouseDown(e)
        
        If e.Button = MouseButtons.Left Then
            isDragging = True
            dragOffset = e.Location
            Me.Cursor = Cursors.SizeAll
        ElseIf e.Button = MouseButtons.Right Then
            ShowContextMenu(e.Location)
        End If
    End Sub

    Protected Overrides Sub OnMouseMove(e As MouseEventArgs)
        MyBase.OnMouseMove(e)
        
        If isDragging Then
            Dim newLocation As Point = Me.PointToScreen(e.Location)
            newLocation.Offset(-dragOffset.X, -dragOffset.Y)
            Me.Location = newLocation
        End If
    End Sub

    Protected Overrides Sub OnMouseUp(e As MouseEventArgs)
        MyBase.OnMouseUp(e)
        
        If e.Button = MouseButtons.Left AndAlso isDragging Then
            isDragging = False
            Me.Cursor = Cursors.Default
            SaveSettings()
        End If
    End Sub

    Private Sub ShowContextMenu(location As Point)
        Dim contextMenu As New ContextMenuStrip()
        
        ' 字体大小菜单
        Dim fontSizeMenu As New ToolStripMenuItem("字体大小")
        fontSizeMenu.DropDownItems.Add(CreateFontSizeItem("小 (32)", 32))
        fontSizeMenu.DropDownItems.Add(CreateFontSizeItem("中 (48)", 48))
        fontSizeMenu.DropDownItems.Add(CreateFontSizeItem("大 (64)", 64))
        fontSizeMenu.DropDownItems.Add(CreateFontSizeItem("特大 (80)", 80))
        
        ' 颜色菜单
        Dim colorMenu As New ToolStripMenuItem("文字颜色")
        colorMenu.DropDownItems.Add(CreateColorItem("白色", Color.White))
        colorMenu.DropDownItems.Add(CreateColorItem("红色", Color.Red))
        colorMenu.DropDownItems.Add(CreateColorItem("绿色", Color.Lime))
        colorMenu.DropDownItems.Add(CreateColorItem("蓝色", Color.Cyan))
        colorMenu.DropDownItems.Add(CreateColorItem("黄色", Color.Yellow))
        colorMenu.DropDownItems.Add(CreateColorItem("自定义...", Color.Empty))
        
        ' 透明度菜单
        Dim transparencyMenu As New ToolStripMenuItem("透明度")
        transparencyMenu.DropDownItems.Add(CreateTransparencyItem("不透明", 255))
        transparencyMenu.DropDownItems.Add(CreateTransparencyItem("轻微透明", 220))
        transparencyMenu.DropDownItems.Add(CreateTransparencyItem("中等透明", 180))
        transparencyMenu.DropDownItems.Add(CreateTransparencyItem("高度透明", 120))
        
        ' 其他选项
        Dim topMostItem As New ToolStripMenuItem("始终置顶")
        topMostItem.Checked = _topMost
        AddHandler topMostItem.Click, Sub() ToggleTopMost()
        
        Dim showSecondsItem As New ToolStripMenuItem("显示秒数")
        showSecondsItem.Checked = _showSeconds
        AddHandler showSecondsItem.Click, Sub() ToggleShowSeconds()
        
        Dim format24Item As New ToolStripMenuItem("24小时制")
        format24Item.Checked = _is24Hour
        AddHandler format24Item.Click, Sub() Toggle24Hour()
        
        ' 关于和退出
        Dim aboutItem As New ToolStripMenuItem("关于")
        AddHandler aboutItem.Click, Sub() ShowAbout()
        
        Dim exitItem As New ToolStripMenuItem("退出")
        AddHandler exitItem.Click, Sub() Me.Close()
        
        ' 添加到菜单
        contextMenu.Items.AddRange({fontSizeMenu, colorMenu, transparencyMenu,
                                   New ToolStripSeparator(),
                                   topMostItem, showSecondsItem, format24Item,
                                   New ToolStripSeparator(),
                                   aboutItem, exitItem})
        
        ' 显示菜单
        contextMenu.Show(Me, location)
    End Sub

    Private Function CreateFontSizeItem(text As String, size As Integer) As ToolStripMenuItem
        Dim item As New ToolStripMenuItem(text)
        item.Checked = (_fontSize = size)
        AddHandler item.Click, Sub() ChangeFontSize(size)
        Return item
    End Function

    Private Function CreateColorItem(text As String, color As Color) As ToolStripMenuItem
        Dim item As New ToolStripMenuItem(text)
        item.Checked = (_textColor = color AndAlso color <> Color.Empty)
        AddHandler item.Click, Sub() ChangeColor(color)
        Return item
    End Function

    Private Function CreateTransparencyItem(text As String, alpha As Integer) As ToolStripMenuItem
        Dim item As New ToolStripMenuItem(text)
        item.Checked = (_transparency = alpha)
        AddHandler item.Click, Sub() ChangeTransparency(alpha)
        Return item
    End Function

    Private Sub ChangeFontSize(size As Integer)
        _fontSize = size
        UpdateFont()
        Me.Invalidate()
        SaveSettings()
    End Sub

    Private Sub ChangeColor(color As Color)
        If color = Color.Empty Then
            ' 显示颜色选择对话框
            Dim colorDialog As New ColorDialog()
            colorDialog.Color = _textColor
            If colorDialog.ShowDialog() = DialogResult.OK Then
                _textColor = colorDialog.Color
                textBrush.Color = _textColor
                Me.Invalidate()
                SaveSettings()
            End If
        Else
            _textColor = color
            textBrush.Color = _textColor
            Me.Invalidate()
            SaveSettings()
        End If
    End Sub

    Private Sub ChangeTransparency(alpha As Integer)
        _transparency = alpha
        Me.Opacity = alpha / 255.0
        SaveSettings()
    End Sub

    Private Sub ToggleTopMost()
        _topMost = Not _topMost
        Me.TopMost = _topMost
        SaveSettings()
    End Sub

    Private Sub ToggleShowSeconds()
        _showSeconds = Not _showSeconds
        Me.Invalidate()
        SaveSettings()
    End Sub

    Private Sub Toggle24Hour()
        _is24Hour = Not _is24Hour
        Me.Invalidate()
        SaveSettings()
    End Sub

    Private Sub ShowAbout()
        MessageBox.Show("极简桌面时钟 v1.0" & vbCrLf & vbCrLf &
                       "功能特性：" & vbCrLf &
                       "• 透明置顶显示" & vbCrLf &
                       "• 左键拖拽移动" & vbCrLf &
                       "• 右键设置菜单" & vbCrLf &
                       "• 自动保存配置" & vbCrLf &
                       "• 多种显示选项" & vbCrLf & vbCrLf &
                       "技术：VB.NET + Windows Forms",
                       "关于桌面时钟", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub LoadSettings()
        Try
            Using key As RegistryKey = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY)
                If key IsNot Nothing Then
                    _fontSize = CInt(key.GetValue("FontSize", 48))
                    _textColor = Color.FromArgb(CInt(key.GetValue("TextColor", Color.White.ToArgb())))
                    _transparency = CInt(key.GetValue("Transparency", 200))
                    _topMost = CBool(key.GetValue("TopMost", True))
                    _showSeconds = CBool(key.GetValue("ShowSeconds", True))
                    _is24Hour = CBool(key.GetValue("Is24Hour", True))
                    
                    Dim x As Integer = CInt(key.GetValue("LocationX", 100))
                    Dim y As Integer = CInt(key.GetValue("LocationY", 100))
                    Me.Location = New Point(x, y)
                End If
            End Using
        Catch
            ' 使用默认设置
        End Try
    End Sub

    Private Sub SaveSettings()
        Try
            Using key As RegistryKey = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY)
                key.SetValue("FontSize", _fontSize)
                key.SetValue("TextColor", _textColor.ToArgb())
                key.SetValue("Transparency", _transparency)
                key.SetValue("TopMost", _topMost)
                key.SetValue("ShowSeconds", _showSeconds)
                key.SetValue("Is24Hour", _is24Hour)
                key.SetValue("LocationX", Me.Location.X)
                key.SetValue("LocationY", Me.Location.Y)
            End Using
        Catch
            ' 忽略保存错误
        End Try
    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        SaveSettings()
        
        ' 清理资源
        If clockTimer IsNot Nothing Then
            clockTimer.Dispose()
        End If
        If clockFont IsNot Nothing Then
            clockFont.Dispose()
        End If
        If textBrush IsNot Nothing Then
            textBrush.Dispose()
        End If
        If shadowBrush IsNot Nothing Then
            shadowBrush.Dispose()
        End If
        
        MyBase.OnFormClosing(e)
    End Sub
End Class
