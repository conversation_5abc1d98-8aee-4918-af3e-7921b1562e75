@echo off
echo Building Desktop Clock with CMake...

REM Check if CMake is available
where cmake >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo CMake not found. Please install CMake.
    echo You can download it from: https://cmake.org/download/
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

REM Configure project
echo Configuring project...
cmake .. -DCMAKE_BUILD_TYPE=Release
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed
    cd ..
    pause
    exit /b 1
)

REM Build project
echo Building project...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed
    cd ..
    pause
    exit /b 1
)

REM Copy executable to parent directory
if exist Release\DesktopClock.exe (
    copy Release\DesktopClock.exe ..\DesktopClock.exe
) else if exist DesktopClock.exe (
    copy DesktopClock.exe ..\DesktopClock.exe
)

cd ..

if exist DesktopClock.exe (
    echo Build successful! Generated: DesktopClock.exe
    dir DesktopClock.exe
    echo.
    echo File size:
    for %%I in (DesktopClock.exe) do echo %%~zI bytes
) else (
    echo Build failed - executable not found!
)

pause
