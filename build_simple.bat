@echo off
echo 尝试编译桌面时钟程序...

REM 尝试直接使用系统路径中的编译器
where cl >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo 找到 cl 编译器，开始编译...
    goto :compile
)

REM 尝试设置 Visual Studio 环境
echo 正在查找 Visual Studio...

REM VS 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到 VS 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    goto :compile
)

REM VS 2022 Professional  
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到 VS 2022 Professional
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    goto :compile
)

REM VS 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到 VS 2019 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    goto :compile
)

REM 如果都没找到，尝试使用 MSBuild
echo 未找到 Visual Studio，尝试使用 MSBuild...
where msbuild >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo 找到 MSBuild，但需要项目文件...
    goto :no_compiler
)

:no_compiler
echo.
echo ========================================
echo 错误: 未找到编译器
echo ========================================
echo.
echo 请安装以下任一工具:
echo 1. Visual Studio 2019/2022 Community (免费)
echo    下载: https://visualstudio.microsoft.com/vs/community/
echo.
echo 2. Visual Studio Build Tools
echo    下载: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
echo.
echo 3. MinGW-w64 (通过 MSYS2)
echo    下载: https://www.msys2.org/
echo.
echo 安装后重新运行此脚本
pause
exit /b 1

:compile
echo 开始编译 ClockApp.cpp...

cl /nologo /EHsc /O2 /MT /DNDEBUG /DUNICODE /D_UNICODE ClockApp.cpp /link user32.lib gdi32.lib kernel32.lib advapi32.lib comdlg32.lib /SUBSYSTEM:WINDOWS /OUT:DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo.
    
    if exist DesktopClock.exe (
        echo 生成的文件:
        dir DesktopClock.exe
        echo.
        echo 程序功能:
        echo ✓ 透明置顶显示
        echo ✓ 左键拖拽移动
        echo ✓ 右键菜单设置
        echo ✓ 自动保存配置
        echo ✓ 颜色和透明度调节
        echo.
        echo 双击 DesktopClock.exe 运行程序
    )
) else (
    echo.
    echo ========================================
    echo 编译失败
    echo ========================================
    echo 请检查上面的错误信息
)

REM 清理临时文件
if exist *.obj del *.obj >nul 2>&1
if exist *.pdb del *.pdb >nul 2>&1

echo.
pause
