<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AssemblyTitle>极简桌面时钟</AssemblyTitle>
    <AssemblyDescription>轻量级透明桌面时钟程序</AssemblyDescription>
    <AssemblyCompany>Desktop Clock</AssemblyCompany>
    <AssemblyProduct>Desktop Clock</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <StartupObject>DesktopClock.Program</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <Optimize>True</Optimize>
    <DebugType>none</DebugType>
    <DebugSymbols>False</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>

</Project>
