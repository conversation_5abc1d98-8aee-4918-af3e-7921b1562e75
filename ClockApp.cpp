#define UNICODE
#define _UNICODE
#include <windows.h>
#include <windowsx.h>
#include <string>
#include <sstream>
#include <iomanip>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")
#pragma comment(lib, "advapi32.lib")

const wchar_t* CLASS_NAME = L"DesktopClockApp";
HWND g_hWnd = nullptr;
HFONT g_hFont = nullptr;
bool g_isDragging = false;
POINT g_dragOffset = {0, 0};
COLORREF g_textColor = RGB(255, 255, 255);
int g_transparency = 200;

std::wstring GetCurrentTimeString() {
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    std::wostringstream oss;
    oss << std::setfill(L'0') << std::setw(2) << st.wHour << L":"
        << std::setfill(L'0') << std::setw(2) << st.wMinute << L":"
        << std::setfill(L'0') << std::setw(2) << st.wSecond;
    
    return oss.str();
}

void SaveSettings() {
    HKEY hKey;
    if (RegCreateKeyEx(HKEY_CURRENT_USER, L"SOFTWARE\\DesktopClockApp", 0, nullptr,
                      REG_OPTION_NON_VOLATILE, KEY_WRITE, nullptr, &hKey, nullptr) == ERROR_SUCCESS) {
        
        RECT rect;
        GetWindowRect(g_hWnd, &rect);
        RegSetValueEx(hKey, L"WindowRect", 0, REG_BINARY, (BYTE*)&rect, sizeof(RECT));
        
        RegSetValueEx(hKey, L"TextColor", 0, REG_DWORD, (BYTE*)&g_textColor, sizeof(DWORD));
        RegSetValueEx(hKey, L"Transparency", 0, REG_DWORD, (BYTE*)&g_transparency, sizeof(DWORD));
        
        RegCloseKey(hKey);
    }
}

void LoadSettings() {
    HKEY hKey;
    if (RegOpenKeyEx(HKEY_CURRENT_USER, L"SOFTWARE\\DesktopClockApp", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        DWORD dwType, dwSize;
        
        RECT rect;
        dwSize = sizeof(RECT);
        if (RegQueryValueEx(hKey, L"WindowRect", nullptr, &dwType, (BYTE*)&rect, &dwSize) == ERROR_SUCCESS) {
            SetWindowPos(g_hWnd, nullptr, rect.left, rect.top, 
                        rect.right - rect.left, rect.bottom - rect.top, 
                        SWP_NOZORDER | SWP_NOACTIVATE);
        }
        
        dwSize = sizeof(DWORD);
        RegQueryValueEx(hKey, L"TextColor", nullptr, &dwType, (BYTE*)&g_textColor, &dwSize);
        
        dwSize = sizeof(DWORD);
        RegQueryValueEx(hKey, L"Transparency", nullptr, &dwType, (BYTE*)&g_transparency, &dwSize);
        
        RegCloseKey(hKey);
    }
}

void OnPaint(HWND hWnd) {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hWnd, &ps);
    
    // 创建内存DC进行双缓冲
    RECT clientRect;
    GetClientRect(hWnd, &clientRect);
    
    HDC memDC = CreateCompatibleDC(hdc);
    HBITMAP memBitmap = CreateCompatibleBitmap(hdc, clientRect.right, clientRect.bottom);
    HBITMAP oldBitmap = (HBITMAP)SelectObject(memDC, memBitmap);
    
    // 清除背景
    HBRUSH bgBrush = CreateSolidBrush(RGB(0, 0, 0));
    FillRect(memDC, &clientRect, bgBrush);
    DeleteObject(bgBrush);
    
    // 设置文字属性
    SetBkMode(memDC, TRANSPARENT);
    HFONT oldFont = (HFONT)SelectObject(memDC, g_hFont);
    
    // 获取时间字符串
    std::wstring timeStr = GetCurrentTimeString();
    
    // 计算文字大小和位置
    SIZE textSize;
    GetTextExtentPoint32(memDC, timeStr.c_str(), timeStr.length(), &textSize);
    
    int x = (clientRect.right - textSize.cx) / 2;
    int y = (clientRect.bottom - textSize.cy) / 2;
    
    // 绘制阴影
    SetTextColor(memDC, RGB(64, 64, 64));
    TextOut(memDC, x + 2, y + 2, timeStr.c_str(), timeStr.length());
    
    // 绘制主文字
    SetTextColor(memDC, g_textColor);
    TextOut(memDC, x, y, timeStr.c_str(), timeStr.length());
    
    // 复制到窗口
    BitBlt(hdc, 0, 0, clientRect.right, clientRect.bottom, memDC, 0, 0, SRCCOPY);
    
    // 清理
    SelectObject(memDC, oldFont);
    SelectObject(memDC, oldBitmap);
    DeleteObject(memBitmap);
    DeleteDC(memDC);
    
    EndPaint(hWnd, &ps);
}

void ShowContextMenu(HWND hWnd, int x, int y) {
    HMENU hMenu = CreatePopupMenu();
    AppendMenu(hMenu, MF_STRING, 1001, L"选择颜色");
    AppendMenu(hMenu, MF_STRING, 1002, L"调整透明度");
    AppendMenu(hMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenu(hMenu, MF_STRING, 1003, L"关于");
    AppendMenu(hMenu, MF_STRING, 1004, L"退出");
    
    POINT pt = {x, y};
    ClientToScreen(hWnd, &pt);
    
    int cmd = TrackPopupMenu(hMenu, TPM_RETURNCMD | TPM_RIGHTBUTTON, pt.x, pt.y, 0, hWnd, nullptr);
    
    switch (cmd) {
    case 1001: // 选择颜色
        {
            CHOOSECOLOR cc = {};
            static COLORREF customColors[16] = {0};
            cc.lStructSize = sizeof(CHOOSECOLOR);
            cc.hwndOwner = hWnd;
            cc.rgbResult = g_textColor;
            cc.lpCustColors = customColors;
            cc.Flags = CC_FULLOPEN | CC_RGBINIT;
            
            if (ChooseColor(&cc)) {
                g_textColor = cc.rgbResult;
                InvalidateRect(hWnd, nullptr, FALSE);
                SaveSettings();
            }
        }
        break;
        
    case 1002: // 调整透明度
        {
            wchar_t buffer[32];
            swprintf_s(buffer, L"%d", g_transparency);
            
            if (IDOK == MessageBox(hWnd, L"当前透明度: 50-255\n(数值越大越不透明)", L"透明度设置", MB_OKCANCEL)) {
                // 简单的透明度调整 - 在实际应用中可以使用滑块控件
                g_transparency = (g_transparency == 200) ? 150 : 200;
                SetLayeredWindowAttributes(hWnd, RGB(0, 0, 0), g_transparency, LWA_COLORKEY | LWA_ALPHA);
                SaveSettings();
            }
        }
        break;
        
    case 1003: // 关于
        MessageBox(hWnd, 
            L"极简桌面时钟 v1.0\n\n"
            L"功能:\n"
            L"• 左键拖拽移动\n"
            L"• 右键打开菜单\n"
            L"• 自动保存设置\n"
            L"• 透明置顶显示\n\n"
            L"技术: Win32 API + GDI", 
            L"关于", MB_OK | MB_ICONINFORMATION);
        break;
        
    case 1004: // 退出
        PostQuitMessage(0);
        break;
    }
    
    DestroyMenu(hMenu);
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        {
            // 创建字体
            g_hFont = CreateFont(
                48, 0, 0, 0, FW_LIGHT, FALSE, FALSE, FALSE,
                DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_SWISS,
                L"Segoe UI Light"
            );
            
            // 加载设置
            LoadSettings();
            
            // 设置透明度
            SetLayeredWindowAttributes(hWnd, RGB(0, 0, 0), g_transparency, LWA_COLORKEY | LWA_ALPHA);
            
            // 启动定时器
            SetTimer(hWnd, 1, 1000, nullptr);
        }
        return 0;
        
    case WM_PAINT:
        OnPaint(hWnd);
        return 0;
        
    case WM_TIMER:
        InvalidateRect(hWnd, nullptr, FALSE);
        return 0;
        
    case WM_LBUTTONDOWN:
        g_isDragging = true;
        SetCapture(hWnd);
        {
            POINT pt = {GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)};
            ClientToScreen(hWnd, &pt);
            RECT windowRect;
            GetWindowRect(hWnd, &windowRect);
            g_dragOffset.x = pt.x - windowRect.left;
            g_dragOffset.y = pt.y - windowRect.top;
        }
        return 0;
        
    case WM_LBUTTONUP:
        if (g_isDragging) {
            g_isDragging = false;
            ReleaseCapture();
            SaveSettings();
        }
        return 0;
        
    case WM_MOUSEMOVE:
        if (g_isDragging) {
            POINT pt = {GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)};
            ClientToScreen(hWnd, &pt);
            int newX = pt.x - g_dragOffset.x;
            int newY = pt.y - g_dragOffset.y;
            SetWindowPos(hWnd, nullptr, newX, newY, 0, 0, 
                        SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);
        }
        return 0;
        
    case WM_RBUTTONUP:
        ShowContextMenu(hWnd, GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
        return 0;
        
    case WM_DESTROY:
        SaveSettings();
        KillTimer(hWnd, 1);
        if (g_hFont) {
            DeleteObject(g_hFont);
        }
        PostQuitMessage(0);
        return 0;
    }
    
    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE, PWSTR, int) {
    // 防止多实例运行
    HANDLE hMutex = CreateMutex(nullptr, TRUE, L"DesktopClockAppMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        CloseHandle(hMutex);
        return 0;
    }
    
    // 注册窗口类
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH);
    
    if (!RegisterClass(&wc)) {
        CloseHandle(hMutex);
        return -1;
    }
    
    // 创建窗口
    g_hWnd = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
        CLASS_NAME,
        L"Desktop Clock",
        WS_POPUP,
        100, 100, 300, 100,
        nullptr, nullptr, hInstance, nullptr
    );
    
    if (!g_hWnd) {
        CloseHandle(hMutex);
        return -1;
    }
    
    // 显示窗口
    ShowWindow(g_hWnd, SW_SHOW);
    UpdateWindow(g_hWnd);
    
    // 消息循环
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理
    CloseHandle(hMutex);
    return 0;
}
