<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桌面时钟演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .clock-demo {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            padding: 40px;
            margin: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .clock {
            font-family: 'Segoe UI Light', 'Segoe UI', sans-serif;
            font-size: 4em;
            font-weight: 300;
            text-align: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            letter-spacing: 2px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 40px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .feature ul {
            list-style: none;
            padding: 0;
        }
        
        .feature li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature li:last-child {
            border-bottom: none;
        }
        
        .feature li::before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }
        
        .tech-specs {
            background: rgba(255, 255, 255, 0.05);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            max-width: 800px;
        }
        
        .tech-specs h3 {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .spec-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .spec-value {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .spec-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .download-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .download-btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            max-width: 800px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin-bottom: 20px;
        }
        
        .instructions ol {
            text-align: left;
            line-height: 1.8;
        }
        
        .instructions li {
            margin-bottom: 10px;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🕐 极简桌面时钟</h1>
        <p>轻量级、高效的Windows桌面时钟程序</p>
    </div>
    
    <div class="clock-demo">
        <div class="clock" id="clock">00:00:00</div>
    </div>
    
    <div class="features">
        <div class="feature">
            <h3>🎨 视觉效果</h3>
            <ul>
                <li>透明背景，完美融入桌面</li>
                <li>平滑字体渲染，高质量显示</li>
                <li>文字阴影效果，提升可读性</li>
                <li>现代Segoe UI Light字体</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>⚙️ 自定义选项</h3>
            <ul>
                <li>字体类型和大小调节</li>
                <li>任意颜色选择</li>
                <li>50-255级透明度控制</li>
                <li>可选择始终置顶</li>
                <li>12/24小时制切换</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🖱️ 交互功能</h3>
            <ul>
                <li>左键拖拽移动位置</li>
                <li>右键菜单快速设置</li>
                <li>自动保存位置和设置</li>
                <li>窗口大小自动调整</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🚀 性能优化</h3>
            <ul>
                <li>内存使用 < 5MB</li>
                <li>CPU占用 < 0.1%</li>
                <li>双缓冲绘制，流畅显示</li>
                <li>智能重绘，节省资源</li>
            </ul>
        </div>
    </div>
    
    <div class="tech-specs">
        <h3>📊 技术规格</h3>
        <div class="specs-grid">
            <div class="spec-item">
                <div class="spec-value">< 5MB</div>
                <div class="spec-label">内存占用</div>
            </div>
            <div class="spec-item">
                <div class="spec-value">< 0.1%</div>
                <div class="spec-label">CPU使用率</div>
            </div>
            <div class="spec-item">
                <div class="spec-value">1秒</div>
                <div class="spec-label">更新频率</div>
            </div>
            <div class="spec-item">
                <div class="spec-value">Win7+</div>
                <div class="spec-label">系统要求</div>
            </div>
        </div>
    </div>
    
    <div class="instructions">
        <h3>🛠️ 编译说明</h3>
        <ol>
            <li>安装Visual Studio 2022或MinGW-w64</li>
            <li>克隆或下载项目文件</li>
            <li>使用以下命令之一编译：</li>
        </ol>
        
        <div class="code-block">
            # 使用Visual Studio
            .\build.bat
            
            # 使用MinGW-w64
            .\build_mingw.bat
            
            # 使用CMake
            .\build_cmake.bat
        </div>
        
        <p>编译完成后会生成 <strong>DesktopClock.exe</strong> 文件</p>
    </div>
    
    <div class="download-section">
        <a href="#" class="download-btn" onclick="alert('请按照编译说明自行编译程序')">
            📥 获取源代码
        </a>
    </div>
    
    <script>
        function updateClock() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            document.getElementById('clock').textContent = `${hours}:${minutes}:${seconds}`;
        }
        
        // 更新时钟
        updateClock();
        setInterval(updateClock, 1000);
        
        // 添加一些动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.animationDelay = `${index * 0.1}s`;
                feature.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });
        
        // CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .feature {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
