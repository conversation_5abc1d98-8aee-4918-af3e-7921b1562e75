# 极简桌面时钟

一个轻量级、高效的Windows桌面时钟程序，具有现代化的界面和丰富的自定义选项。

## 特性

### 🎨 视觉效果
- **透明背景**：可调节透明度，完美融入桌面
- **平滑字体渲染**：使用GDI+实现高质量文字显示
- **阴影效果**：文字带有柔和阴影，提升可读性
- **现代字体**：默认使用Segoe UI Light字体

### ⚙️ 自定义选项
- **字体设置**：可选择字体类型和大小
- **颜色自定义**：支持任意颜色选择
- **透明度调节**：50-255级别透明度控制
- **置顶显示**：可选择是否始终置顶
- **时间格式**：支持12/24小时制，可显示/隐藏秒数

### 🖱️ 交互功能
- **拖拽移动**：左键拖拽可移动时钟位置
- **右键菜单**：右键点击显示设置菜单
- **位置记忆**：自动保存窗口位置和所有设置
- **自动调整**：窗口大小根据字体大小自动调整

### 🚀 性能优化
- **低资源占用**：内存使用 < 5MB
- **低CPU占用**：< 0.1%（每秒更新一次）
- **双缓冲绘制**：消除闪烁，流畅显示
- **智能重绘**：仅在需要时更新显示

## 系统要求

- Windows 7 或更高版本
- Visual C++ 2022 Redistributable (x64)
- 支持DPI缩放

## 编译说明

### 使用Visual Studio 2022
1. 打开`DesktopClock.vcxproj`项目文件
2. 选择Release x64配置
3. 生成解决方案

### 使用命令行编译
运行`build.bat`脚本：
```batch
build.bat
```

编译完成后会生成`DesktopClock.exe`文件。

## 使用说明

### 启动程序
双击`DesktopClock.exe`启动程序。程序会自动显示在桌面上。

### 基本操作
- **移动时钟**：左键拖拽时钟到任意位置
- **打开设置**：右键点击时钟，选择相应菜单项
- **退出程序**：右键菜单选择"退出"

### 设置选项
右键菜单提供以下选项：
- **字体设置**：选择字体类型和大小
- **颜色设置**：自定义文字颜色
- **透明度**：调节窗口透明度
- **置顶显示**：切换是否始终置顶

### 配置文件
程序设置自动保存到Windows注册表：
```
HKEY_CURRENT_USER\SOFTWARE\DesktopClock
```

## 技术实现

### 核心技术
- **Win32 API**：原生Windows API实现
- **GDI+**：高质量图形渲染
- **分层窗口**：实现透明效果
- **注册表**：配置持久化存储

### 架构设计
- **单例模式**：防止多实例运行
- **RAII**：自动资源管理
- **智能指针**：内存安全
- **消息驱动**：响应式架构

### 性能优化
- **双缓冲绘制**：避免闪烁
- **按需更新**：减少不必要的重绘
- **资源复用**：GDI对象缓存
- **静态链接**：减少依赖

## 故障排除

### 常见问题
1. **程序无法启动**
   - 检查是否安装了Visual C++ Redistributable
   - 确认系统版本兼容性

2. **字体显示异常**
   - 重置字体设置到默认值
   - 检查系统字体是否完整

3. **透明效果不工作**
   - 确认系统支持分层窗口
   - 检查桌面合成是否启用

### 重置设置
删除注册表项可重置所有设置：
```
HKEY_CURRENT_USER\SOFTWARE\DesktopClock
```

## 开发计划

### 未来功能
- [ ] 多时区支持
- [ ] 皮肤主题系统
- [ ] 日历弹出面板
- [ ] 系统监控显示
- [ ] 自然语言时间

### 优化计划
- [ ] 更好的DPI适配
- [ ] 动画效果
- [ ] 更多字体选项
- [ ] 快捷键支持

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**享受您的极简桌面时钟体验！** ⏰
