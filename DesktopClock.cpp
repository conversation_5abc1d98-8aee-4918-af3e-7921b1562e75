#include "DesktopClock.h"
#include <sstream>
#include <iomanip>
#include <windowsx.h>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")
#pragma comment(lib, "advapi32.lib")

DesktopClock::DesktopClock(HINSTANCE hInstance) 
    : m_hInstance(hInstance), m_hWnd(nullptr), m_gdiplusToken(0) {
}

DesktopClock::~DesktopClock() {
    Cleanup();
}

bool DesktopClock::Initialize() {
    // 设置DPI感知
    SetProcessDPIAware();

    // 初始化 GDI+
    GdiplusStartupInput gdiplusStartupInput;
    Status status = GdiplusStartup(&m_gdiplusToken, &gdiplusStartupInput, nullptr);
    if (status != Ok) {
        return false;
    }

    // 初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_STANDARD_CLASSES | ICC_BAR_CLASSES;
    if (!InitCommonControlsEx(&icex)) {
        return false;
    }
    
    // 加载配置
    LoadConfig();
    
    // 创建窗口
    if (!CreateClockWindow()) {
        return false;
    }
    
    // 初始化绘图资源
    HDC hdc = GetDC(m_hWnd);
    if (hdc) {
        m_graphics = std::make_unique<Graphics>(hdc);
        if (m_graphics) {
            m_graphics->SetTextRenderingHint(TextRenderingHintAntiAlias);
            m_graphics->SetSmoothingMode(SmoothingModeAntiAlias);
        }
        ReleaseDC(m_hWnd, hdc);
    }
    
    UpdateFont();
    ApplySettings();
    
    // 启动定时器
    SetTimer(m_hWnd, IDT_TIMER, 1000, nullptr);
    
    return true;
}

void DesktopClock::Cleanup() {
    if (m_hWnd) {
        KillTimer(m_hWnd, IDT_TIMER);
        DestroyWindow(m_hWnd);
        m_hWnd = nullptr;
    }
    
    // 清理 GDI+ 资源
    m_font.reset();
    m_textBrush.reset();
    m_shadowBrush.reset();
    m_graphics.reset();
    
    if (m_gdiplusToken) {
        GdiplusShutdown(m_gdiplusToken);
        m_gdiplusToken = 0;
    }
}

bool DesktopClock::CreateClockWindow() {
    const wchar_t* className = L"DesktopClockWindow";
    
    // 注册窗口类
    WNDCLASSEXW wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEXW);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = WindowProc;
    wcex.hInstance = m_hInstance;
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = nullptr;  // 透明背景
    wcex.lpszClassName = className;
    
    if (!RegisterClassExW(&wcex)) {
        return false;
    }
    
    // 创建窗口
    m_hWnd = CreateWindowExW(
        WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,  // 分层窗口、置顶、工具窗口
        className,
        L"桌面时钟",
        WS_POPUP,  // 无边框弹出窗口
        m_config.position.left,
        m_config.position.top,
        m_config.position.right - m_config.position.left,
        m_config.position.bottom - m_config.position.top,
        nullptr,
        nullptr,
        m_hInstance,
        this  // 传递this指针
    );
    
    return m_hWnd != nullptr;
}

LRESULT CALLBACK DesktopClock::WindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    DesktopClock* pClock = nullptr;
    
    if (message == WM_NCCREATE) {
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        pClock = reinterpret_cast<DesktopClock*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hWnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pClock));
    } else {
        pClock = reinterpret_cast<DesktopClock*>(GetWindowLongPtr(hWnd, GWLP_USERDATA));
    }
    
    if (pClock) {
        return pClock->HandleMessage(message, wParam, lParam);
    }
    
    return DefWindowProc(hWnd, message, wParam, lParam);
}

LRESULT DesktopClock::HandleMessage(UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_PAINT:
        OnPaint();
        return 0;
        
    case WM_TIMER:
        if (wParam == IDT_TIMER) {
            OnTimer();
        }
        return 0;
        
    case WM_LBUTTONDOWN:
        OnLeftButtonDown(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
        return 0;
        
    case WM_LBUTTONUP:
        OnLeftButtonUp();
        return 0;
        
    case WM_MOUSEMOVE:
        OnMouseMove(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
        return 0;
        
    case WM_RBUTTONUP:
        OnRightClick(GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam));
        return 0;
        
    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case IDM_SETTINGS:
        case IDM_FONT:
        case IDM_COLOR:
        case IDM_TRANSPARENCY:
            ShowSettingsDialog();
            break;
        case IDM_TOPMOST:
            m_config.topmost = !m_config.topmost;
            UpdateWindowStyle();
            SaveConfig();
            break;
        case IDM_EXIT:
            PostQuitMessage(0);
            break;
        }
        return 0;
        
    case WM_DESTROY:
        SaveConfig();
        PostQuitMessage(0);
        return 0;
        
    default:
        return DefWindowProc(m_hWnd, message, wParam, lParam);
    }
}

void DesktopClock::OnPaint() {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(m_hWnd, &ps);

    // 获取客户区大小
    RECT clientRect;
    GetClientRect(m_hWnd, &clientRect);

    // 创建内存DC进行双缓冲绘制
    HDC memDC = CreateCompatibleDC(hdc);
    HBITMAP memBitmap = CreateCompatibleBitmap(hdc, clientRect.right, clientRect.bottom);
    HBITMAP oldBitmap = (HBITMAP)SelectObject(memDC, memBitmap);

    // 清除背景（透明）
    HBRUSH clearBrush = CreateSolidBrush(RGB(0, 0, 0));
    FillRect(memDC, &clientRect, clearBrush);
    DeleteObject(clearBrush);

    // 使用GDI+绘制
    Graphics graphics(memDC);
    graphics.SetTextRenderingHint(TextRenderingHintAntiAlias);
    graphics.SetSmoothingMode(SmoothingModeAntiAlias);

    // 获取当前时间字符串
    std::wstring timeStr = GetCurrentTimeString();

    if (m_font) {
        // 计算文字位置（居中）
        RectF layoutRect(0.0f, 0.0f, (REAL)clientRect.right, (REAL)clientRect.bottom);
        RectF boundingBox;
        graphics.MeasureString(timeStr.c_str(), -1, m_font.get(), layoutRect, &boundingBox);

        REAL x = (clientRect.right - boundingBox.Width) / 2;
        REAL y = (clientRect.bottom - boundingBox.Height) / 2;

        // 绘制阴影
        if (m_shadowBrush) {
            graphics.DrawString(timeStr.c_str(), -1, m_font.get(),
                              PointF(x + 2, y + 2), m_shadowBrush.get());
        }

        // 绘制主文字
        if (m_textBrush) {
            graphics.DrawString(timeStr.c_str(), -1, m_font.get(),
                              PointF(x, y), m_textBrush.get());
        }
    }

    // 复制到窗口DC
    BitBlt(hdc, 0, 0, clientRect.right, clientRect.bottom, memDC, 0, 0, SRCCOPY);

    // 清理资源
    SelectObject(memDC, oldBitmap);
    DeleteObject(memBitmap);
    DeleteDC(memDC);

    EndPaint(m_hWnd, &ps);
}

void DesktopClock::OnTimer() {
    InvalidateWindow();
}

void DesktopClock::InvalidateWindow() {
    InvalidateRect(m_hWnd, nullptr, FALSE);
}

std::wstring DesktopClock::GetCurrentTimeString() {
    SYSTEMTIME st;
    GetLocalTime(&st);

    std::wostringstream oss;

    if (m_config.is24Hour) {
        oss << std::setfill(L'0') << std::setw(2) << st.wHour << L":"
            << std::setfill(L'0') << std::setw(2) << st.wMinute;

        if (m_config.showSeconds) {
            oss << L":" << std::setfill(L'0') << std::setw(2) << st.wSecond;
        }
    } else {
        // 12小时制
        int hour12 = st.wHour % 12;
        if (hour12 == 0) hour12 = 12;

        oss << hour12 << L":"
            << std::setfill(L'0') << std::setw(2) << st.wMinute;

        if (m_config.showSeconds) {
            oss << L":" << std::setfill(L'0') << std::setw(2) << st.wSecond;
        }

        oss << (st.wHour < 12 ? L" AM" : L" PM");
    }

    return oss.str();
}

void DesktopClock::UpdateFont() {
    m_font.reset();
    m_textBrush.reset();
    m_shadowBrush.reset();

    // 创建字体
    FontFamily fontFamily(m_config.fontFamily.c_str());
    m_font = std::make_unique<Font>(&fontFamily, (REAL)m_config.fontSize, FontStyleRegular, UnitPixel);

    // 创建画刷
    m_textBrush = std::make_unique<SolidBrush>(m_config.textColor);
    m_shadowBrush = std::make_unique<SolidBrush>(Color(90, 0, 0, 0));  // 半透明黑色阴影
}

void DesktopClock::OnLeftButtonDown(int x, int y) {
    m_isDragging = true;
    SetCapture(m_hWnd);

    // 记录拖拽偏移量
    POINT pt = {x, y};
    ClientToScreen(m_hWnd, &pt);
    RECT windowRect;
    GetWindowRect(m_hWnd, &windowRect);

    m_dragOffset.x = pt.x - windowRect.left;
    m_dragOffset.y = pt.y - windowRect.top;
}

void DesktopClock::OnLeftButtonUp() {
    if (m_isDragging) {
        m_isDragging = false;
        ReleaseCapture();

        // 保存新位置
        GetWindowRect(m_hWnd, &m_config.position);
        SaveConfig();
    }
}

void DesktopClock::OnMouseMove(int x, int y) {
    if (m_isDragging) {
        POINT pt = {x, y};
        ClientToScreen(m_hWnd, &pt);

        int newX = pt.x - m_dragOffset.x;
        int newY = pt.y - m_dragOffset.y;

        SetWindowPos(m_hWnd, nullptr, newX, newY, 0, 0,
                    SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);
    }
}

void DesktopClock::OnRightClick(int x, int y) {
    POINT pt = {x, y};
    ClientToScreen(m_hWnd, &pt);

    HMENU hMenu = LoadMenu(m_hInstance, MAKEINTRESOURCE(IDM_SETTINGS));
    HMENU hSubMenu = GetSubMenu(hMenu, 0);

    // 更新菜单项状态
    CheckMenuItem(hSubMenu, IDM_TOPMOST,
                 MF_BYCOMMAND | (m_config.topmost ? MF_CHECKED : MF_UNCHECKED));

    // 显示上下文菜单
    TrackPopupMenu(hSubMenu, TPM_RIGHTBUTTON, pt.x, pt.y, 0, m_hWnd, nullptr);

    DestroyMenu(hMenu);
}

void DesktopClock::ShowSettingsDialog() {
    DialogBoxParam(m_hInstance, MAKEINTRESOURCE(IDD_SETTINGS),
                  m_hWnd, SettingsDialogProc, reinterpret_cast<LPARAM>(this));
}

INT_PTR CALLBACK DesktopClock::SettingsDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam) {
    DesktopClock* pClock = nullptr;

    if (message == WM_INITDIALOG) {
        pClock = reinterpret_cast<DesktopClock*>(lParam);
        SetWindowLongPtr(hDlg, DWLP_USER, lParam);
    } else {
        pClock = reinterpret_cast<DesktopClock*>(GetWindowLongPtr(hDlg, DWLP_USER));
    }

    if (!pClock) return FALSE;

    switch (message) {
    case WM_INITDIALOG:
        {
            // 初始化控件值
            SetDlgItemInt(hDlg, IDC_FONT_SIZE, pClock->m_config.fontSize, FALSE);

            // 设置透明度滑块
            HWND hSlider = GetDlgItem(hDlg, IDC_TRANSPARENCY);
            SendMessage(hSlider, TBM_SETRANGE, TRUE, MAKELONG(50, 255));
            SendMessage(hSlider, TBM_SETPOS, TRUE, pClock->m_config.transparency);

            // 设置置顶复选框
            CheckDlgButton(hDlg, IDC_TOPMOST, pClock->m_config.topmost ? BST_CHECKED : BST_UNCHECKED);
        }
        return TRUE;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case IDC_FONT_BUTTON:
            {
                CHOOSEFONT cf = {};
                LOGFONT lf = {};

                cf.lStructSize = sizeof(CHOOSEFONT);
                cf.hwndOwner = hDlg;
                cf.lpLogFont = &lf;
                cf.Flags = CF_SCREENFONTS | CF_INITTOLOGFONTSTRUCT;

                // 设置当前字体
                wcscpy_s(lf.lfFaceName, pClock->m_config.fontFamily.c_str());
                lf.lfHeight = -pClock->m_config.fontSize;

                if (ChooseFont(&cf)) {
                    pClock->m_config.fontFamily = lf.lfFaceName;
                    pClock->m_config.fontSize = abs(lf.lfHeight);
                    SetDlgItemInt(hDlg, IDC_FONT_SIZE, pClock->m_config.fontSize, FALSE);
                }
            }
            break;

        case IDC_COLOR_BUTTON:
            {
                CHOOSECOLOR cc = {};
                static COLORREF customColors[16] = {0};

                cc.lStructSize = sizeof(CHOOSECOLOR);
                cc.hwndOwner = hDlg;
                cc.rgbResult = RGB(pClock->m_config.textColor.GetR(),
                                 pClock->m_config.textColor.GetG(),
                                 pClock->m_config.textColor.GetB());
                cc.lpCustColors = customColors;
                cc.Flags = CC_FULLOPEN | CC_RGBINIT;

                if (ChooseColor(&cc)) {
                    pClock->m_config.textColor = Color(255,
                        GetRValue(cc.rgbResult),
                        GetGValue(cc.rgbResult),
                        GetBValue(cc.rgbResult));
                }
            }
            break;

        case IDOK:
            {
                // 获取设置值
                pClock->m_config.fontSize = GetDlgItemInt(hDlg, IDC_FONT_SIZE, nullptr, FALSE);

                HWND hSlider = GetDlgItem(hDlg, IDC_TRANSPARENCY);
                pClock->m_config.transparency = (int)SendMessage(hSlider, TBM_GETPOS, 0, 0);

                pClock->m_config.topmost = IsDlgButtonChecked(hDlg, IDC_TOPMOST) == BST_CHECKED;

                // 应用设置
                pClock->ApplySettings();
                pClock->SaveConfig();

                EndDialog(hDlg, IDOK);
            }
            return TRUE;

        case IDCANCEL:
            EndDialog(hDlg, IDCANCEL);
            return TRUE;
        }
        break;
    }

    return FALSE;
}

void DesktopClock::ApplySettings() {
    // 更新字体
    UpdateFont();

    // 更新窗口样式
    UpdateWindowStyle();

    // 设置透明度
    SetLayeredWindowAttributes(m_hWnd, 0, m_config.transparency, LWA_ALPHA);

    // 重绘窗口
    InvalidateWindow();
}

void DesktopClock::UpdateWindowStyle() {
    DWORD exStyle = GetWindowLong(m_hWnd, GWL_EXSTYLE);

    if (m_config.topmost) {
        SetWindowPos(m_hWnd, HWND_TOPMOST, 0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);
    } else {
        SetWindowPos(m_hWnd, HWND_NOTOPMOST, 0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);
    }
}

void DesktopClock::LoadConfig() {
    HKEY hKey;
    if (RegOpenKeyEx(HKEY_CURRENT_USER, REGISTRY_KEY, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        DWORD dwType, dwSize;

        // 加载字体大小
        dwSize = sizeof(DWORD);
        RegQueryValueEx(hKey, L"FontSize", nullptr, &dwType,
                       reinterpret_cast<LPBYTE>(&m_config.fontSize), &dwSize);

        // 加载透明度
        dwSize = sizeof(DWORD);
        RegQueryValueEx(hKey, L"Transparency", nullptr, &dwType,
                       reinterpret_cast<LPBYTE>(&m_config.transparency), &dwSize);

        // 加载置顶设置
        DWORD topmost;
        dwSize = sizeof(DWORD);
        if (RegQueryValueEx(hKey, L"Topmost", nullptr, &dwType,
                           reinterpret_cast<LPBYTE>(&topmost), &dwSize) == ERROR_SUCCESS) {
            m_config.topmost = (topmost != 0);
        }

        // 加载颜色
        DWORD color;
        dwSize = sizeof(DWORD);
        if (RegQueryValueEx(hKey, L"TextColor", nullptr, &dwType,
                           reinterpret_cast<LPBYTE>(&color), &dwSize) == ERROR_SUCCESS) {
            m_config.textColor = Color(
                (color >> 24) & 0xFF,  // Alpha
                (color >> 16) & 0xFF,  // Red
                (color >> 8) & 0xFF,   // Green
                color & 0xFF           // Blue
            );
        }

        // 加载窗口位置
        dwSize = sizeof(RECT);
        RegQueryValueEx(hKey, L"Position", nullptr, &dwType,
                       reinterpret_cast<LPBYTE>(&m_config.position), &dwSize);

        // 加载字体名称
        wchar_t fontName[256];
        dwSize = sizeof(fontName);
        if (RegQueryValueEx(hKey, L"FontFamily", nullptr, &dwType,
                           reinterpret_cast<LPBYTE>(fontName), &dwSize) == ERROR_SUCCESS) {
            m_config.fontFamily = fontName;
        }

        // 加载其他设置
        DWORD showSeconds, is24Hour;
        dwSize = sizeof(DWORD);
        if (RegQueryValueEx(hKey, L"ShowSeconds", nullptr, &dwType,
                           reinterpret_cast<LPBYTE>(&showSeconds), &dwSize) == ERROR_SUCCESS) {
            m_config.showSeconds = (showSeconds != 0);
        }

        dwSize = sizeof(DWORD);
        if (RegQueryValueEx(hKey, L"Is24Hour", nullptr, &dwType,
                           reinterpret_cast<LPBYTE>(&is24Hour), &dwSize) == ERROR_SUCCESS) {
            m_config.is24Hour = (is24Hour != 0);
        }

        RegCloseKey(hKey);
    }
}

void DesktopClock::SaveConfig() {
    HKEY hKey;
    if (RegCreateKeyEx(HKEY_CURRENT_USER, REGISTRY_KEY, 0, nullptr,
                      REG_OPTION_NON_VOLATILE, KEY_WRITE, nullptr, &hKey, nullptr) == ERROR_SUCCESS) {

        // 保存字体大小
        RegSetValueEx(hKey, L"FontSize", 0, REG_DWORD,
                     reinterpret_cast<const BYTE*>(&m_config.fontSize), sizeof(DWORD));

        // 保存透明度
        RegSetValueEx(hKey, L"Transparency", 0, REG_DWORD,
                     reinterpret_cast<const BYTE*>(&m_config.transparency), sizeof(DWORD));

        // 保存置顶设置
        DWORD topmost = m_config.topmost ? 1 : 0;
        RegSetValueEx(hKey, L"Topmost", 0, REG_DWORD,
                     reinterpret_cast<const BYTE*>(&topmost), sizeof(DWORD));

        // 保存颜色
        DWORD color = (m_config.textColor.GetA() << 24) |
                     (m_config.textColor.GetR() << 16) |
                     (m_config.textColor.GetG() << 8) |
                     m_config.textColor.GetB();
        RegSetValueEx(hKey, L"TextColor", 0, REG_DWORD,
                     reinterpret_cast<const BYTE*>(&color), sizeof(DWORD));

        // 保存窗口位置
        RegSetValueEx(hKey, L"Position", 0, REG_BINARY,
                     reinterpret_cast<const BYTE*>(&m_config.position), sizeof(RECT));

        // 保存字体名称
        RegSetValueEx(hKey, L"FontFamily", 0, REG_SZ,
                     reinterpret_cast<const BYTE*>(m_config.fontFamily.c_str()),
                     (m_config.fontFamily.length() + 1) * sizeof(wchar_t));

        // 保存其他设置
        DWORD showSeconds = m_config.showSeconds ? 1 : 0;
        RegSetValueEx(hKey, L"ShowSeconds", 0, REG_DWORD,
                     reinterpret_cast<const BYTE*>(&showSeconds), sizeof(DWORD));

        DWORD is24Hour = m_config.is24Hour ? 1 : 0;
        RegSetValueEx(hKey, L"Is24Hour", 0, REG_DWORD,
                     reinterpret_cast<const BYTE*>(&is24Hour), sizeof(DWORD));

        RegCloseKey(hKey);
    }
}

RECT DesktopClock::GetTextBounds(const std::wstring& text) {
    RECT bounds = {0, 0, 0, 0};

    if (m_font && m_graphics) {
        RectF layoutRect(0, 0, 1000, 1000);  // 足够大的区域
        RectF boundingBox;

        Status status = m_graphics->MeasureString(text.c_str(), -1, m_font.get(),
                                                 layoutRect, &boundingBox);

        if (status == Ok) {
            bounds.right = (LONG)ceil(boundingBox.Width) + 20;   // 添加边距
            bounds.bottom = (LONG)ceil(boundingBox.Height) + 20;  // 添加边距
        }
    }

    // 设置最小尺寸
    if (bounds.right < 100) bounds.right = 100;
    if (bounds.bottom < 50) bounds.bottom = 50;

    return bounds;
}

void DesktopClock::UpdateTime() {
    std::wstring timeStr = GetCurrentTimeString();
    RECT textBounds = GetTextBounds(timeStr);

    // 获取当前窗口位置
    RECT currentRect;
    GetWindowRect(m_hWnd, &currentRect);

    // 如果尺寸需要调整
    int currentWidth = currentRect.right - currentRect.left;
    int currentHeight = currentRect.bottom - currentRect.top;

    if (currentWidth != textBounds.right || currentHeight != textBounds.bottom) {
        SetWindowPos(m_hWnd, nullptr, 0, 0, textBounds.right, textBounds.bottom,
                    SWP_NOMOVE | SWP_NOZORDER | SWP_NOACTIVATE);

        // 更新配置中的位置信息
        GetWindowRect(m_hWnd, &m_config.position);
    }
}
