@echo off
echo Building Desktop Clock...

if not exist "MiniClock.cpp" (
    echo Error: MiniClock.cpp not found
    pause
    exit /b 1
)

echo Searching for Visual Studio...

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :compile
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto :compile
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :compile
)

where cl >nul 2>&1
if %ERRORLEVEL% == 0 (
    goto :compile
)

echo Visual Studio not found
echo Please install Visual Studio 2022 Community
echo https://visualstudio.microsoft.com/vs/community/
pause
exit /b 1

:compile
echo Compiling...
cl /nologo /EHsc /O2 /MT /DUNICODE /D_UNICODE MiniClock.cpp /link user32.lib gdi32.lib kernel32.lib /SUBSYSTEM:WINDOWS /OUT:DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo.
    echo BUILD SUCCESS!
    echo Generated: DesktopClock.exe
    if exist DesktopClock.exe (
        for %%I in (DesktopClock.exe) do echo Size: %%~zI bytes
    )
    echo.
    echo Double-click DesktopClock.exe to run
) else (
    echo BUILD FAILED
    echo Check error messages above
)

if exist *.obj del *.obj
if exist *.pdb del *.pdb

pause
