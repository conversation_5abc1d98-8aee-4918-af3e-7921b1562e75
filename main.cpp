#include "DesktopClock.h"
#include <memory>

int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
                     _In_opt_ HINSTANCE hPrevInstance,
                     _In_ LPWSTR lpCmdLine,
                     _In_ int nCmdShow) {
    
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);
    
    // 检查是否已有实例运行
    HANDLE hMutex = CreateMutex(nullptr, TRUE, L"DesktopClockMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        CloseHandle(hMutex);
        return 0;  // 已有实例运行，退出
    }
    
    // 创建时钟实例
    auto clock = std::make_unique<DesktopClock>(hInstance);
    
    // 初始化
    if (!clock->Initialize()) {
        MessageBox(nullptr, L"初始化失败！", L"错误", MB_OK | MB_ICONERROR);
        CloseHandle(hMutex);
        return -1;
    }
    
    // 显示窗口
    ShowWindow(clock->GetHWnd(), SW_SHOW);
    UpdateWindow(clock->GetHWnd());
    
    // 消息循环
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理
    CloseHandle(hMutex);
    return (int)msg.wParam;
}
