Imports System
Imports System.Drawing
Imports System.Windows.Forms

Public Class SimpleClockForm
    Inherits Form

    Private WithEvents timer As Timer
    Private isDragging As Boolean = False
    Private dragOffset As Point

    Public Sub New()
        InitializeForm()
        SetupTimer()
    End Sub

    Private Sub InitializeForm()
        ' 基本窗体设置
        Me.Text = "Simple Clock"
        Me.Size = New Size(250, 80)
        Me.StartPosition = FormStartPosition.Manual
        Me.Location = New Point(100, 100)
        Me.FormBorderStyle = FormBorderStyle.None
        Me.BackColor = Color.Black
        Me.ForeColor = Color.White
        Me.TopMost = True
        Me.ShowInTaskbar = False
        
        ' 设置透明
        Me.TransparencyKey = Color.Black
        Me.Opacity = 0.8
        
        ' 启用双缓冲
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or 
                   ControlStyles.UserPaint Or 
                   ControlStyles.DoubleBuffer, True)
    End Sub

    Private Sub SetupTimer()
        timer = New Timer()
        timer.Interval = 1000
        timer.Enabled = True
    End Sub

    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        MyBase.OnPaint(e)
        
        Dim g As Graphics = e.Graphics
        g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        
        ' 获取当前时间
        Dim timeText As String = DateTime.Now.ToString("HH:mm:ss")
        
        ' 创建字体和画刷
        Using font As New Font("Arial", 24, FontStyle.Bold)
            Using brush As New SolidBrush(Color.White)
                Using shadowBrush As New SolidBrush(Color.FromArgb(128, Color.Black))
                    ' 测量文字大小
                    Dim textSize As SizeF = g.MeasureString(timeText, font)
                    
                    ' 计算居中位置
                    Dim x As Single = (Me.Width - textSize.Width) / 2
                    Dim y As Single = (Me.Height - textSize.Height) / 2
                    
                    ' 绘制阴影
                    g.DrawString(timeText, font, shadowBrush, x + 1, y + 1)
                    
                    ' 绘制主文字
                    g.DrawString(timeText, font, brush, x, y)
                End Using
            End Using
        End Using
    End Sub

    Private Sub Timer_Tick(sender As Object, e As EventArgs) Handles timer.Tick
        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnMouseDown(e As MouseEventArgs)
        MyBase.OnMouseDown(e)
        
        If e.Button = MouseButtons.Left Then
            isDragging = True
            dragOffset = e.Location
            Me.Cursor = Cursors.SizeAll
        ElseIf e.Button = MouseButtons.Right Then
            ShowContextMenu()
        End If
    End Sub

    Protected Overrides Sub OnMouseMove(e As MouseEventArgs)
        MyBase.OnMouseMove(e)
        
        If isDragging Then
            Dim newLocation As Point = Me.PointToScreen(e.Location)
            newLocation.Offset(-dragOffset.X, -dragOffset.Y)
            Me.Location = newLocation
        End If
    End Sub

    Protected Overrides Sub OnMouseUp(e As MouseEventArgs)
        MyBase.OnMouseUp(e)
        
        If e.Button = MouseButtons.Left AndAlso isDragging Then
            isDragging = False
            Me.Cursor = Cursors.Default
        End If
    End Sub

    Private Sub ShowContextMenu()
        Dim menu As New ContextMenuStrip()
        
        Dim aboutItem As New ToolStripMenuItem("关于")
        AddHandler aboutItem.Click, Sub()
            MessageBox.Show("简单桌面时钟 v1.0" & vbCrLf & vbCrLf &
                           "功能:" & vbCrLf &
                           "• 透明置顶显示" & vbCrLf &
                           "• 左键拖拽移动" & vbCrLf &
                           "• 右键显示菜单" & vbCrLf & vbCrLf &
                           "技术: VB.NET + Windows Forms",
                           "关于", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Sub
        
        Dim exitItem As New ToolStripMenuItem("退出")
        AddHandler exitItem.Click, Sub() Me.Close()
        
        menu.Items.Add(aboutItem)
        menu.Items.Add(New ToolStripSeparator())
        menu.Items.Add(exitItem)
        
        menu.Show(Me, Me.PointToClient(Cursor.Position))
    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        If timer IsNot Nothing Then
            timer.Dispose()
        End If
        MyBase.OnFormClosing(e)
    End Sub
End Class

' 程序入口点
Public Module Program
    <STAThread>
    Public Sub Main()
        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)
        Application.Run(New SimpleClockForm())
    End Sub
End Module
