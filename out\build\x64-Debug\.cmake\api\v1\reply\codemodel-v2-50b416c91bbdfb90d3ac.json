{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-a673ed7a61062a5fc758.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "DesktopClock", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "DesktopClock::@6890427a1f51a3e7e1df", "jsonFile": "target-DesktopClock-Debug-c354788c4a4ba286c01d.json", "name": "DesktopClock", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/time/out/build/x64-Debug", "source": "C:/Users/<USER>/Desktop/time"}, "version": {"major": 2, "minor": 8}}