#include <windows.h>
#include <string>
#include <sstream>
#include <iomanip>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")

const wchar_t* CLASS_NAME = L"BasicDesktopClock";
HWND g_hWnd = nullptr;
HFONT g_hFont = nullptr;
bool g_isDragging = false;
POINT g_dragOffset = {0, 0};

std::wstring GetCurrentTimeString() {
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    std::wostringstream oss;
    oss << std::setfill(L'0') << std::setw(2) << st.wHour << L":"
        << std::setfill(L'0') << std::setw(2) << st.wMinute << L":"
        << std::setfill(L'0') << std::setw(2) << st.wSecond;
    
    return oss.str();
}

void OnPaint(HWND hWnd) {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hWnd, &ps);
    
    // 设置背景透明
    SetBkMode(hdc, TRANSPARENT);
    
    // 选择字体
    HFONT oldFont = (HFONT)SelectObject(hdc, g_hFont);
    
    // 设置文字颜色
    SetTextColor(hdc, RGB(255, 255, 255));
    
    // 获取时间字符串
    std::wstring timeStr = GetCurrentTimeString();
    
    // 获取客户区
    RECT clientRect;
    GetClientRect(hWnd, &clientRect);
    
    // 计算文字大小
    SIZE textSize;
    GetTextExtentPoint32(hdc, timeStr.c_str(), timeStr.length(), &textSize);
    
    // 计算居中位置
    int x = (clientRect.right - textSize.cx) / 2;
    int y = (clientRect.bottom - textSize.cy) / 2;
    
    // 绘制阴影
    SetTextColor(hdc, RGB(64, 64, 64));
    TextOut(hdc, x + 2, y + 2, timeStr.c_str(), timeStr.length());
    
    // 绘制主文字
    SetTextColor(hdc, RGB(255, 255, 255));
    TextOut(hdc, x, y, timeStr.c_str(), timeStr.length());
    
    // 恢复字体
    SelectObject(hdc, oldFont);
    
    EndPaint(hWnd, &ps);
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        {
            // 创建字体
            g_hFont = CreateFont(
                48,                        // 高度
                0,                         // 宽度
                0,                         // 角度
                0,                         // 基线角度
                FW_LIGHT,                  // 字重
                FALSE,                     // 斜体
                FALSE,                     // 下划线
                FALSE,                     // 删除线
                DEFAULT_CHARSET,           // 字符集
                OUT_DEFAULT_PRECIS,        // 输出精度
                CLIP_DEFAULT_PRECIS,       // 裁剪精度
                CLEARTYPE_QUALITY,         // 质量
                DEFAULT_PITCH | FF_SWISS,  // 间距和族
                L"Segoe UI Light"          // 字体名
            );
            
            // 启动定时器
            SetTimer(hWnd, 1, 1000, nullptr);
        }
        return 0;
        
    case WM_PAINT:
        OnPaint(hWnd);
        return 0;
        
    case WM_TIMER:
        InvalidateRect(hWnd, nullptr, FALSE);
        return 0;
        
    case WM_LBUTTONDOWN:
        g_isDragging = true;
        SetCapture(hWnd);
        {
            POINT pt = {GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)};
            ClientToScreen(hWnd, &pt);
            RECT windowRect;
            GetWindowRect(hWnd, &windowRect);
            g_dragOffset.x = pt.x - windowRect.left;
            g_dragOffset.y = pt.y - windowRect.top;
        }
        return 0;
        
    case WM_LBUTTONUP:
        if (g_isDragging) {
            g_isDragging = false;
            ReleaseCapture();
        }
        return 0;
        
    case WM_MOUSEMOVE:
        if (g_isDragging) {
            POINT pt = {GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)};
            ClientToScreen(hWnd, &pt);
            int newX = pt.x - g_dragOffset.x;
            int newY = pt.y - g_dragOffset.y;
            SetWindowPos(hWnd, nullptr, newX, newY, 0, 0, 
                        SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);
        }
        return 0;
        
    case WM_RBUTTONUP:
        {
            int result = MessageBox(hWnd, 
                L"桌面时钟 v1.0\n\n左键拖拽移动\n右键显示此菜单\n\n点击确定退出程序", 
                L"关于", 
                MB_OKCANCEL | MB_ICONINFORMATION);
            if (result == IDOK) {
                PostQuitMessage(0);
            }
        }
        return 0;
        
    case WM_DESTROY:
        KillTimer(hWnd, 1);
        if (g_hFont) {
            DeleteObject(g_hFont);
        }
        PostQuitMessage(0);
        return 0;
    }
    
    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE, PWSTR, int) {
    // 检查是否已有实例运行
    HANDLE hMutex = CreateMutex(nullptr, TRUE, L"BasicDesktopClockMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        CloseHandle(hMutex);
        return 0;
    }
    
    // 注册窗口类
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH);
    
    RegisterClass(&wc);
    
    // 创建窗口
    g_hWnd = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
        CLASS_NAME,
        L"Basic Desktop Clock",
        WS_POPUP,
        100, 100, 300, 100,
        nullptr, nullptr, hInstance, nullptr
    );
    
    if (!g_hWnd) {
        CloseHandle(hMutex);
        return -1;
    }
    
    // 设置透明度
    SetLayeredWindowAttributes(g_hWnd, RGB(0, 0, 0), 200, LWA_COLORKEY | LWA_ALPHA);
    
    // 显示窗口
    ShowWindow(g_hWnd, SW_SHOW);
    UpdateWindow(g_hWnd);
    
    // 消息循环
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理
    CloseHandle(hMutex);
    return 0;
}
