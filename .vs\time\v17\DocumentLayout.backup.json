{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\time\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.vbproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:DesktopClock.vbproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\CMakeSettings.json||{10608CD5-279C-4A28-BD5F-BA2CFCE06219}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CMakeSettings.json||{10608CD5-279C-4A28-BD5F-BA2CFCE06219}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\build\\DesktopClock.vcxproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:build\\DesktopClock.vcxproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\quick_build.bat||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:quick_build.bat||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:DesktopClock.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.vcxproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:DesktopClock.vcxproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\编译指南.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:编译指南.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.rc||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:DesktopClock.rc||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\main.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:main.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\build.bat||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:build.bat||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\time\\build_cmake.bat||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:build_cmake.bat||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|CMake 概述页||{B1CAA5B0-FEB1-4350-8AB9-F895876842F2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DesktopClock.vbproj", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.vbproj", "RelativeDocumentMoniker": "DesktopClock.vbproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.vbproj", "RelativeToolTip": "DesktopClock.vbproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003380|", "WhenOpened": "2025-07-09T01:19:15.521Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CMakeSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\CMakeSettings.json", "RelativeDocumentMoniker": "CMakeSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\CMakeSettings.json", "RelativeToolTip": "CMakeSettings.json", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-09T01:12:24.657Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DesktopClock.vcxproj", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\build\\DesktopClock.vcxproj", "RelativeDocumentMoniker": "build\\DesktopClock.vcxproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\build\\DesktopClock.vcxproj", "RelativeToolTip": "build\\DesktopClock.vcxproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000698|", "WhenOpened": "2025-07-09T01:11:27.82Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "quick_build.bat", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\quick_build.bat", "RelativeDocumentMoniker": "quick_build.bat", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\quick_build.bat", "RelativeToolTip": "quick_build.bat", "ViewState": "AgIAAGkAAAAAAAAAAAAAAAwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000614|", "WhenOpened": "2025-07-09T01:06:56.594Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "编译指南.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\编译指南.md", "RelativeDocumentMoniker": "编译指南.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\编译指南.md", "RelativeToolTip": "编译指南.md", "ViewState": "AgIAALEAAAAAAAAAAAAAAMIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-09T01:01:26.202Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "DesktopClock.rc", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.rc", "RelativeDocumentMoniker": "DesktopClock.rc", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.rc", "RelativeToolTip": "DesktopClock.rc", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-07-09T01:00:55.181Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "README.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\README.md", "RelativeDocumentMoniker": "README.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\README.md", "RelativeToolTip": "README.md", "ViewState": "AgIAAHgAAAAAAAAAAAAAAKMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-09T00:42:51.609Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "DesktopClock.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.cpp", "RelativeDocumentMoniker": "DesktopClock.cpp", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.cpp", "RelativeToolTip": "DesktopClock.cpp", "ViewState": "AgIAAOcAAAAAAAAAAAAAABEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-09T00:41:01.117Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DesktopClock.vcxproj", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.vcxproj", "RelativeDocumentMoniker": "DesktopClock.vcxproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\DesktopClock.vcxproj", "RelativeToolTip": "DesktopClock.vcxproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAE0AAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000698|", "WhenOpened": "2025-07-09T00:35:36.675Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "build_cmake.bat", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\build_cmake.bat", "RelativeDocumentMoniker": "build_cmake.bat", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\build_cmake.bat", "RelativeToolTip": "build_cmake.bat", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000614|", "WhenOpened": "2025-07-09T00:40:47.325Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CMakeLists.txt", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\CMakeLists.txt", "RelativeDocumentMoniker": "CMakeLists.txt", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\CMakeLists.txt", "RelativeToolTip": "CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-09T00:40:59.95Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "main.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\main.cpp", "RelativeDocumentMoniker": "main.cpp", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\main.cpp", "RelativeToolTip": "main.cpp", "ViewState": "AgIAABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-09T00:42:47.861Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "build.bat", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\time\\build.bat", "RelativeDocumentMoniker": "build.bat", "ToolTip": "C:\\Users\\<USER>\\Desktop\\time\\build.bat", "RelativeToolTip": "build.bat", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000614|", "WhenOpened": "2025-07-09T00:39:28.928Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "CMake 概述页", "DocumentMoniker": "CMake 概述页", "ToolTip": "CMake 概述页", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-09T00:37:45.91Z"}]}, {"DockedWidth": 1229, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}]}]}