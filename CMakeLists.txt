cmake_minimum_required(VERSION 3.16)
project(DesktopClock)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS_RELEASE "/O2 /MT /DNDEBUG")
    set(CMAKE_EXE_LINKER_FLAGS "/SUBSYSTEM:WINDOWS")
else()
    set(CMAKE_CXX_FLAGS_RELEASE "-O2 -s -static")
    set(CMAKE_EXE_LINKER_FLAGS "-mwindows")
endif()

# 定义宏
add_definitions(-DUNICODE -D_UNICODE)

# 源文件
set(SOURCES
    main.cpp
    DesktopClock.cpp
)

# 头文件
set(HEADERS
    DesktopClock.h
    resource.h
)

# 资源文件
set(RESOURCES
    DesktopClock.rc
    app.manifest
)

# 创建可执行文件
add_executable(${PROJECT_NAME} WIN32 ${SOURCES} ${HEADERS} ${RESOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    gdiplus
    comctl32
    user32
    gdi32
    kernel32
    advapi32
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}
)

# 如果使用MSVC，嵌入清单文件
if(MSVC)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/MANIFEST:EMBED /MANIFESTINPUT:${CMAKE_SOURCE_DIR}/app.manifest"
    )
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)
