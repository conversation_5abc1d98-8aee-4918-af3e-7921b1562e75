@echo off
echo Building Desktop Clock...

REM Try different VS paths
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
) else (
    echo Visual Studio not found. Please install Visual Studio 2019 or 2022.
    pause
    exit /b 1
)

REM Compile resource file
rc DesktopClock.rc
if %ERRORLEVEL% neq 0 (
    echo Resource compilation failed
    pause
    exit /b 1
)

REM Compile program
cl /EHsc /std:c++17 /O2 /MT /DNDEBUG /DUNICODE /D_UNICODE main.cpp DesktopClock.cpp DesktopClock.res /link gdiplus.lib comctl32.lib user32.lib gdi32.lib kernel32.lib advapi32.lib /SUBSYSTEM:WINDOWS /MANIFEST:EMBED /MANIFESTINPUT:app.manifest /OUT:DesktopClock.exe

if %ERRORLEVEL% == 0 (
    echo Build successful! Generated: DesktopClock.exe
    dir DesktopClock.exe
) else (
    echo Build failed!
)

REM Clean temporary files
del *.obj 2>nul
del *.res 2>nul

pause
