#include "resource.h"
#include <windows.h>

// 菜单资源
IDM_SETTINGS MENU
BEGIN
    POPUP ""
    BEGIN
        MENUITEM "字体设置(&F)", IDM_FONT
        MENUITEM "颜色设置(&C)", IDM_COLOR
        MENUITEM SEPARATOR
        MENUITEM "透明度(&T)", IDM_TRANSPARENCY
        MENUITEM "置顶显示(&A)", IDM_TOPMOST
        MENUITEM SEPARATOR
        MENUITEM "退出(&X)", IDM_EXIT
    END
END

// 设置对话框
IDD_SETTINGS DIALOGEX 0, 0, 300, 200
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "时钟设置"
FONT 9, "Microsoft YaHei UI"
BEGIN
    GROUPBOX "显示设置", -1, 10, 10, 280, 80
    LTEXT "字体大小:", -1, 20, 30, 50, 12
    EDITTEXT IDC_FONT_SIZE, 80, 28, 50, 14, ES_NUMBER
    PUSHBUTTON "选择字体...", IDC_FONT_BUTTON, 140, 27, 60, 16
    
    LTEXT "透明度:", -1, 20, 50, 50, 12
    CONTROL "", IDC_TRANSPARENCY, "msctls_trackbar32", TBS_HORZ | TBS_BOTH | WS_TABSTOP, 80, 48, 120, 20
    
    PUSHBUTTON "选择颜色...", IDC_COLOR_BUTTON, 20, 70, 60, 16
    CONTROL "始终置顶", IDC_TOPMOST, "Button", BS_AUTOCHECKBOX | WS_TABSTOP, 100, 72, 60, 12
    
    DEFPUSHBUTTON "确定", IDOK, 190, 170, 50, 14
    PUSHBUTTON "取消", IDCANCEL, 245, 170, 50, 14
END

// 字符串表
STRINGTABLE
BEGIN
    IDS_APP_TITLE "桌面时钟"
    IDS_SETTINGS "设置"
    IDS_EXIT "退出"
END
