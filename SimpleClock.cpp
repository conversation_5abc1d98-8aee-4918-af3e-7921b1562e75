#include <windows.h>
#include <gdiplus.h>
#include <string>
#include <sstream>
#include <iomanip>

#pragma comment(lib, "gdiplus.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")

using namespace Gdiplus;

const wchar_t* CLASS_NAME = L"SimpleDesktopClock";
HWND g_hWnd = nullptr;
ULONG_PTR g_gdiplusToken = 0;

std::wstring GetCurrentTimeString() {
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    std::wostringstream oss;
    oss << std::setfill(L'0') << std::setw(2) << st.wHour << L":"
        << std::setfill(L'0') << std::setw(2) << st.wMinute << L":"
        << std::setfill(L'0') << std::setw(2) << st.wSecond;
    
    return oss.str();
}

void OnPaint(HWND hWnd) {
    PAINTSTRUCT ps;
    HDC hdc = BeginPaint(hWnd, &ps);
    
    Graphics graphics(hdc);
    graphics.SetTextRenderingHint(TextRenderingHintAntiAlias);
    
    // 创建字体和画刷
    FontFamily fontFamily(L"Segoe UI Light");
    Font font(&fontFamily, 48, FontStyleRegular, UnitPixel);
    SolidBrush textBrush(Color(255, 255, 255, 255));
    SolidBrush shadowBrush(Color(90, 0, 0, 0));
    
    // 获取时间字符串
    std::wstring timeStr = GetCurrentTimeString();
    
    // 获取客户区
    RECT clientRect;
    GetClientRect(hWnd, &clientRect);
    
    // 计算文字位置
    RectF layoutRect(0.0f, 0.0f, (REAL)clientRect.right, (REAL)clientRect.bottom);
    RectF boundingBox;
    graphics.MeasureString(timeStr.c_str(), -1, &font, layoutRect, &boundingBox);
    
    REAL x = (clientRect.right - boundingBox.Width) / 2;
    REAL y = (clientRect.bottom - boundingBox.Height) / 2;
    
    // 绘制阴影
    graphics.DrawString(timeStr.c_str(), -1, &font, PointF(x + 2, y + 2), &shadowBrush);
    
    // 绘制主文字
    graphics.DrawString(timeStr.c_str(), -1, &font, PointF(x, y), &textBrush);
    
    EndPaint(hWnd, &ps);
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_PAINT:
        OnPaint(hWnd);
        return 0;
        
    case WM_TIMER:
        InvalidateRect(hWnd, nullptr, FALSE);
        return 0;
        
    case WM_DESTROY:
        KillTimer(hWnd, 1);
        PostQuitMessage(0);
        return 0;
        
    case WM_RBUTTONUP:
        PostQuitMessage(0);
        return 0;
    }
    
    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE, PWSTR, int) {
    // 初始化GDI+
    GdiplusStartupInput gdiplusStartupInput;
    if (GdiplusStartup(&g_gdiplusToken, &gdiplusStartupInput, nullptr) != Ok) {
        return -1;
    }
    
    // 注册窗口类
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = nullptr;
    
    RegisterClass(&wc);
    
    // 创建窗口
    g_hWnd = CreateWindowEx(
        WS_EX_LAYERED | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
        CLASS_NAME,
        L"Desktop Clock",
        WS_POPUP,
        100, 100, 300, 100,
        nullptr, nullptr, hInstance, nullptr
    );
    
    if (!g_hWnd) {
        GdiplusShutdown(g_gdiplusToken);
        return -1;
    }
    
    // 设置透明度
    SetLayeredWindowAttributes(g_hWnd, 0, 240, LWA_ALPHA);
    
    // 显示窗口
    ShowWindow(g_hWnd, SW_SHOW);
    UpdateWindow(g_hWnd);
    
    // 启动定时器
    SetTimer(g_hWnd, 1, 1000, nullptr);
    
    // 消息循环
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理
    GdiplusShutdown(g_gdiplusToken);
    return 0;
}
