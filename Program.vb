Imports System
Imports System.Windows.Forms

Namespace DesktopClock
    Friend Module Program
        ''' <summary>
        ''' 应用程序的主入口点。
        ''' </summary>
        <STAThread>
        Sub Main()
            ' 启用应用程序的可视样式
            Application.EnableVisualStyles()
            Application.SetCompatibleTextRenderingDefault(False)
            
            ' 检查是否已有实例运行
            Dim mutexName As String = "DesktopClockVBMutex"
            Dim mutex As New Threading.Mutex(True, mutexName)
            
            If Not mutex.WaitOne(0, False) Then
                ' 已有实例运行，退出
                MessageBox.Show("桌面时钟已在运行中！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            Try
                ' 运行主窗体
                Application.Run(New ClockForm())
            Finally
                ' 释放互斥锁
                mutex.ReleaseMutex()
                mutex.Dispose()
            End Try
        End Sub
    End Module
End Namespace
