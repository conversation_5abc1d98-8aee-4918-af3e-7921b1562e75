{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6-msvc6", "suffix": "msvc6"}}, "objects": [{"jsonFile": "codemodel-v2-50b416c91bbdfb90d3ac.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-0d6c89963801b723a368.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bf8c9156c4287255595d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-00a0d399699ea41065d4.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-0d6c89963801b723a368.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bf8c9156c4287255595d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "codemodel-v2-50b416c91bbdfb90d3ac.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-00a0d399699ea41065d4.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}